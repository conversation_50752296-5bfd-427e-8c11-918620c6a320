<template>
  <div class="api-detail" v-if="apiInfo && apiInfo.id">
    <!-- 顶部操作栏 -->
    <div class="detail-header">
      <div class="header-tabs">
        <div class="tab-item" :class="{ active: activeTab === 'doc' }" @click="activeTab = 'doc'">文档</div>
        <div class="tab-item" :class="{ active: activeTab === 'edit' }" @click="activeTab = 'edit'">修改文档</div>
        <div class="tab-item" :class="{ active: activeTab === 'run' }" @click="activeTab = 'run'">运行</div>
        <div class="tab-item" :class="{ active: activeTab === 'mock' }" @click="activeTab = 'mock'">高级 Mock</div>
      </div>
      <div class="header-actions">
        <el-button type="primary" size="small" icon="Share">分享</el-button>
        <el-button size="small" icon="Code">生成代码</el-button>
      </div>
    </div>

    <!-- 接口基本信息 -->
    <div class="api-info-section">
      <div class="api-title-row">
        <h2 class="api-title">{{ apiInfo.name || '接口名称' }}</h2>
        <div class="api-method">
          <el-tag
            :type="getMethodType(apiInfo.method)"
            size="large"
            class="method-tag"
          >
            {{ apiInfo.method }}
          </el-tag>
          <span class="api-path">{{ apiInfo.path }}</span>
        </div>
      </div>
      <div class="api-meta">
        <span class="meta-item">创建时间: {{ formatDate(apiInfo.createTime) }}</span>
        <span class="meta-item">修改时间: {{ formatDate(apiInfo.updateTime) }}</span>
        <span class="meta-item">修改者: {{ apiInfo.updateBy || apiInfo.createBy }}</span>
        <span class="meta-item">创建者: {{ apiInfo.createBy }}</span>
        <span class="meta-item">目录: {{ moduleName || '默认' }}</span>
      </div>
    </div>

    <!-- API历史记录 -->
    <ApiHistory :api-id="apiId" ref="historyRef" v-if="activeTab === 'doc'" />

    <!-- Mock 环境配置 -->
    <div class="mock-section" v-if="activeTab === 'run'">
      <div class="mock-header">
        <span class="mock-title">Mock</span>
        <el-dropdown>
          <span class="mock-dropdown">
            本地 Mock <i class="el-icon-arrow-down"></i>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>本地 Mock</el-dropdown-item>
              <el-dropdown-item>云端 Mock</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <div class="mock-config">
        <div class="config-row">
          <div class="config-label">名称</div>
          <div class="config-label">来源</div>
          <div class="config-label">URL / 参数</div>
          <div class="config-actions"></div>
        </div>
        <div class="config-row config-data">
          <div class="config-value">
            <el-select v-model="selectedEnv" placeholder="选择环境" @change="onEnvChange" size="small">
              <el-option
                v-for="env in envList"
                :key="env.id"
                :label="env.envName"
                :value="env.id"
              />
            </el-select>
          </div>
          <div class="config-value">接口地址</div>
          <div class="config-value url-display">{{ fullUrl }}</div>
          <div class="config-actions">
            <el-button type="primary" size="small" @click="sendRequest" :loading="requesting">
              {{ requesting ? '发送中' : '保存' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-section">
      <!-- 文档标签页内容 -->
      <div v-if="activeTab === 'doc'" class="doc-content">
        <!-- 请求参数部分 -->
        <div class="params-section">
          <h3 class="section-title">请求参数</h3>

          <!-- Header 参数 -->
          <div v-if="headerParams.length > 0" class="param-group">
            <div class="param-group-header">
              <span class="group-title">Header 参数</span>
              <el-button type="primary" size="small" plain @click="openAddParamDialog('header')">添加参数</el-button>
            </div>
            <div class="param-list">
              <div
                v-for="param in headerParams"
                :key="param.id"
                class="param-item"
              >
                <div class="param-info">
                  <span class="param-name" :class="{ required: param.required }">{{ param.name }}</span>
                  <span class="param-type">{{ param.type }}</span>
                  <span class="param-required-badge" v-if="param.required">必需</span>
                </div>
                <div class="param-desc" v-if="param.description">{{ param.description }}</div>
                <div class="param-actions">
                  <el-button size="mini" type="primary" @click="openEditParamDialog(param)">编辑</el-button>
                  <el-button size="mini" type="danger" @click="deleteParam(param)">删除</el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- Body 参数 -->
          <div v-if="bodyParams.length > 0" class="param-group">
            <div class="param-group-header">
              <span class="group-title">Body 参数</span>
              <div class="body-type-selector">
                <span class="body-type-label">application/json</span>
              </div>
            </div>

            <div class="body-content">
              <div class="body-left">
                <!-- 参数列表 -->
                <div class="param-tree">
                  <div
                    v-for="param in flatBodyParams"
                    :key="param.id"
                    class="tree-item"
                    :style="{ paddingLeft: (param.level * 20) + 'px' }"
                  >
                    <div class="tree-item-content">
                      <span class="param-name" :class="{ required: param.required }">{{ param.name }}</span>
                      <span class="param-type">{{ param.type }}</span>
                      <span class="param-required-badge" v-if="param.required">必需</span>
                    </div>
                    <div class="param-desc" v-if="param.description">{{ param.description }}</div>
                    <div class="param-actions">
                      <el-button size="mini" type="success" @click="openAddParamDialog('body', param.id)">添加子参数</el-button>
                      <el-button size="mini" type="primary" @click="openEditParamDialog(param)">编辑</el-button>
                      <el-button size="mini" type="danger" @click="deleteParam(param)">删除</el-button>
                    </div>
                  </div>
                </div>
              </div>

              <div class="body-right">
                <!-- JSON 示例 -->
                <div class="json-example">
                  <div class="example-header">示例</div>
                  <div class="json-content">
                    <pre class="json-code">{{ generateJsonExample() }}</pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>

      <!-- 运行标签页内容 -->
      <div v-if="activeTab === 'run'" class="run-content">
        <ApiDebugger
          :api-info="apiInfo"
          :header-params="headerParams"
          :path-params="pathParams"
          :body-params="bodyParams"
          :response-params="responseParams"
        />
      </div>

      <!-- 编辑标签页内容 -->
      <div v-if="activeTab === 'edit'" class="edit-content">
        <div class="edit-actions">
          <el-button type="primary" @click="editApi">编辑基本信息</el-button>
          <el-button type="danger" @click="deleteApi">删除接口</el-button>
        </div>

        <!-- 参数管理 -->
        <div class="param-management">
          <h3>参数管理</h3>

          <!-- Header 参数表格 -->
          <div class="param-table-section">
            <div class="table-header">
              <h4>Header 参数</h4>
              <el-button type="primary" size="small" @click="openAddParamDialog('header')">添加参数</el-button>
            </div>
            <el-table :data="headerParams" border>
              <el-table-column prop="name" label="参数名" width="180"></el-table-column>
              <el-table-column prop="type" label="数据类型" width="120"></el-table-column>
              <el-table-column label="是否必填" width="100">
                <template #default="scope">
                  {{ scope.row.required === 1 ? '是' : '否' }}
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述"></el-table-column>
              <el-table-column label="操作" width="220">
                <template #default="scope">
                  <el-button size="mini" type="primary" @click="openEditParamDialog(scope.row)">编辑</el-button>
                  <el-button size="mini" type="danger" @click="deleteParam(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- Body 参数表格 -->
          <div class="param-table-section">
            <div class="table-header">
              <h4>Body 参数</h4>
              <el-button type="primary" size="small" @click="openAddParamDialog('body')">添加参数</el-button>
            </div>
            <el-table
              :data="bodyParams"
              border
              row-key="id"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              default-expand-all
            >
              <el-table-column prop="name" label="参数名" width="180"></el-table-column>
              <el-table-column prop="type" label="数据类型" width="120"></el-table-column>
              <el-table-column label="是否必填" width="100">
                <template #default="scope">
                  {{ scope.row.required === 1 ? '是' : '否' }}
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述"></el-table-column>
              <el-table-column label="操作" width="220">
                <template #default="scope">
                  <el-button size="mini" type="success" @click="openAddParamDialog('body', scope.row.id)">添加子参数</el-button>
                  <el-button size="mini" type="primary" @click="openEditParamDialog(scope.row)">编辑</el-button>
                  <el-button size="mini" type="danger" @click="deleteParam(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 响应参数表格 -->
          <div class="param-table-section">
            <div class="table-header">
              <h4>响应参数</h4>
              <el-button type="primary" size="small" @click="openAddParamDialog('response')">添加参数</el-button>
            </div>
            <el-table
              :data="responseParams"
              border
              row-key="id"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              default-expand-all
            >
              <el-table-column prop="name" label="参数名" width="180"></el-table-column>
              <el-table-column prop="type" label="数据类型" width="120"></el-table-column>
              <el-table-column label="是否必填" width="100">
                <template #default="scope">
                  {{ scope.row.required === 1 ? '是' : '否' }}
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述"></el-table-column>
              <el-table-column label="操作" width="220">
                <template #default="scope">
                  <el-button size="mini" type="success" @click="openAddParamDialog('response', scope.row.id)">添加子参数</el-button>
                  <el-button size="mini" type="primary" @click="openEditParamDialog(scope.row)">编辑</el-button>
                  <el-button size="mini" type="danger" @click="deleteParam(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 示例管理 -->
          <div class="example-management">
            <div class="table-header">
              <h4>接口示例</h4>
              <el-button type="primary" size="small" @click="openExampleDialog()">编辑示例</el-button>
            </div>

            <div v-if="pairedExamples && pairedExamples.length > 0">
              <el-tabs type="border-card">
                <el-tab-pane
                  v-for="(pair, index) in pairedExamples"
                  :key="index"
                  :label="getPairLabel(pair)"
                >
                  <div class="example-pair">
                    <div class="example-item">
                      <div class="example-header">请求示例</div>
                      <div class="code-block-wrapper">
                        <pre class="code-block">{{ pair.request ? pair.request.content : '无请求示例' }}</pre>
                      </div>
                    </div>
                    <div class="example-item">
                      <div class="example-header">响应示例</div>
                      <div class="code-block-wrapper">
                        <pre class="code-block">{{ pair.response ? pair.response.content : '无响应示例' }}</pre>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
            <div v-else class="empty-data">暂无接口示例</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 参数编辑对话框 -->
    <el-dialog
      :title="paramDialog.isEdit ? '编辑参数' : '添加参数'"
      v-model="paramDialog.visible"
      width="500px"
      @closed="closeParamDialog"
    >
      <el-form ref="paramFormRef" :model="paramDialog.form" :rules="paramDialog.rules" label-width="100px">
        <el-form-item label="参数名称" prop="name">
          <el-input v-model="paramDialog.form.name" placeholder="请输入参数名称"></el-input>
        </el-form-item>
        
        <el-form-item label="数据类型" prop="type">
          <el-select v-model="paramDialog.form.type" placeholder="请选择数据类型">
            <el-option label="String" value="String"></el-option>
            <el-option label="Integer" value="Integer"></el-option>
            <el-option label="Long" value="Long"></el-option>
            <el-option label="Float" value="Float"></el-option>
            <el-option label="Double" value="Double"></el-option>
            <el-option label="Boolean" value="Boolean"></el-option>
            <el-option label="Date" value="Date"></el-option>
            <el-option label="Object" value="Object"></el-option>
            <el-option label="Array" value="Array"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="参数位置" prop="paramType">
          <el-select v-model="paramDialog.form.paramType" placeholder="请选择参数位置" :disabled="paramDialog.allowedTypes.length === 1">
            <el-option
              v-for="type in paramDialog.allowedTypes"
              :key="type"
              :label="type === 'header' ? 'Header' : type === 'path' ? 'Path' : type === 'body' ? 'Body' : '响应'"
              :value="type"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="参数描述" prop="description">
          <el-input
            v-model="paramDialog.form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入参数描述"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="是否必填">
          <el-switch
            v-model="paramDialog.form.required"
            :active-value="1"
            :inactive-value="0"
          ></el-switch>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeParamDialog">取消</el-button>
          <el-button type="primary" @click="saveParameter">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- API编辑对话框 -->
    <el-dialog
      title="编辑API基本信息"
      v-model="editApiDialog.visible"
      width="600px"
      @closed="closeEditApiDialog"
    >
      <el-form ref="editApiFormRef" :model="editApiDialog.form" :rules="editApiDialog.rules" label-width="100px">
        <el-form-item label="API名称" prop="name">
          <el-input v-model="editApiDialog.form.name" placeholder="请输入API名称"></el-input>
        </el-form-item>
        
        <el-form-item label="请求方法" prop="method">
          <el-select v-model="editApiDialog.form.method" placeholder="请选择请求方法">
            <el-option label="GET" value="GET"></el-option>
            <el-option label="POST" value="POST"></el-option>
            <el-option label="PUT" value="PUT"></el-option>
            <el-option label="DELETE" value="DELETE"></el-option>
            <el-option label="PATCH" value="PATCH"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="接口路径" prop="path">
          <el-input v-model="editApiDialog.form.path" placeholder="请输入接口路径"></el-input>
        </el-form-item>
        
        <el-form-item label="开发状态" prop="devStatus">
          <el-select v-model="editApiDialog.form.devStatus" placeholder="请选择开发状态">
            <el-option
              v-for="(name, code) in devStatusMap"
              :key="code"
              :label="name"
              :value="code"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="接口描述" prop="description">
          <el-input
            v-model="editApiDialog.form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入接口描述"
          ></el-input>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeEditApiDialog">取消</el-button>
          <el-button type="primary" @click="saveApiInfo">保存</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 示例编辑对话框 -->
    <el-dialog
      title="编辑接口示例"
      v-model="exampleDialog.visible"
      width="900px"
      @closed="closeExampleDialog"
    >
      <div v-if="exampleDialog.pairs.length > 0">
        <el-tabs v-model="exampleDialog.activeTab" type="card" closable @tab-remove="removeExamplePair">
          <el-tab-pane
            v-for="(pair, index) in exampleDialog.pairs"
            :key="pair.id || ('temp_' + index)"
            :label="pair.name || '示例 ' + (index + 1)"
            :name="String(pair.id || ('temp_' + index))"
          >
            <el-form label-width="100px">
              <el-form-item label="示例名称">
                <el-input v-model="pair.name" placeholder="请输入示例名称"></el-input>
              </el-form-item>
              
              <el-form-item label="设为默认">
                <el-checkbox 
                  v-model="pair.isDefault" 
                  :true-label="1" 
                  :false-label="0"
                  @change="val => {
                    if (val === 1) {
                      exampleDialog.pairs.forEach(p => {
                        if (p !== pair) p.isDefault = 0
                      })
                    }
                  }"
                ></el-checkbox>
              </el-form-item>
              
              <div class="example-edit-pair">
                <div class="example-edit-item">
                  <div class="example-edit-header">请求示例</div>
                  <el-input
                    v-model="pair.requestContent"
                    type="textarea"
                    :rows="15"
                    placeholder="请输入请求示例内容（JSON格式）"
                  ></el-input>
                </div>
                <div class="example-edit-item">
                  <div class="example-edit-header">响应示例</div>
                  <el-input
                    v-model="pair.responseContent"
                    type="textarea"
                    :rows="15"
                    placeholder="请输入响应示例内容（JSON格式）"
                  ></el-input>
                </div>
              </div>
            </el-form>
          </el-tab-pane>
        </el-tabs>
        
        <div class="dialog-action">
          <el-button type="primary" plain @click="addNewExamplePair">添加示例</el-button>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeExampleDialog">取消</el-button>
          <el-button type="primary" @click="saveExamplePairs">保存</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 状态更新对话框 -->
    <StatusUpdateDialog 
      v-model:visible="statusUpdateDialogVisible" 
      :api="apiInfo" 
      @updated="handleStatusUpdated" 
    />
  </div>
</template>

<script>
import { computed, ref, watch, onMounted, nextTick } from 'vue'
import { useStore } from 'vuex'
import request from '@/utils/request'
import {
  deleteParameter,
  updateParameter,
  getExampleList,
  batchSaveExamples,
  getParameterTree,
  createParameter,
  getApiDevStatusEnum,
  updateApi,
  getGroupDetail
} from '@/api/api'
import { getEnvList } from '@/api/env'
import { ElMessage } from 'element-plus'
import ApiHistory from './ApiHistory.vue'
import StatusUpdateDialog from './StatusUpdateDialog.vue'
import ApiDebugger from './ApiDebugger.vue'
import axios from 'axios'

export default {
  name: 'ApiDetail',
  components: {
    ApiHistory,
    StatusUpdateDialog,
    ApiDebugger
  },
  props: {
    apiId: {
      type: String,
      required: true
    }
  },
  emits: ['edit', 'deleted'],
  setup(props, { emit }) {
    const store = useStore()
    const apiInfo = ref({})
    const headerParams = ref([])
    const pathParams = ref([])
    const bodyParams = ref([])
    const responseParams = ref([])
    const apiRequestExamples = ref([])
    const pairedExamples = ref([])
    const loading = ref(false)
    const devStatusMap = ref({}) // 用于存储状态枚举映射
    const creatorName = ref('')
    const historyRef = ref(null)
    const statusUpdateDialogVisible = ref(false)
    const moduleName = ref('') // 存储实际模块名称
    const activeTab = ref('doc') // 当前激活的标签页

    // 环境和调试相关
    const envList = ref([])
    const selectedEnv = ref('')
    const currentBaseUrl = ref('')
    const requesting = ref(false)

    // 监听apiId的变化，当apiId变化时重新加载详情
    watch(() => props.apiId, (newApiId, oldApiId) => {
      if (newApiId && newApiId !== oldApiId) {
        fetchApiDetail()
      }
    })

    // 监听apiInfo变化，获取创建人姓名和模块名称
    watch(
      () => apiInfo.value,
      async (newApiInfo) => {
        // 获取创建人姓名
        if (newApiInfo?.createBy) {
          creatorName.value = await getUserName(newApiInfo.createBy)
        } else {
          creatorName.value = ''
        }
        
        // 获取实际模块名称
        if (newApiInfo?.groupId) {
          try {
            const res = await getGroupDetail(newApiInfo.groupId)
            if (res.code === 200 && res.data) {
              moduleName.value = res.data.name
            }
          } catch (error) {
            console.error('获取模块名称失败', error)
            moduleName.value = ''
          }
        } else {
          moduleName.value = ''
        }
      },
      { deep: true, immediate: true }
    )

    // 参数对话框
    const paramDialog = ref({
      visible: false,
      isEdit: false,
      allowedTypes: [],
      parentId: null,
      form: {
        name: '',
        type: 'String',
        paramType: '',
        description: '',
        required: 0
      },
      rules: {
        name: [{ required: true, message: '请输入参数名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
        paramType: [{ required: true, message: '请选择参数位置', trigger: 'change' }]
      }
    })

    // 示例对话框
    const exampleDialog = ref({
      visible: false,
      activeTab: '',
      pairs: [],
      newExampleCount: 0
    })

    // API编辑对话框
    const editApiDialog = ref({
      visible: false,
      form: {
        id: '',
        name: '',
        method: '',
        path: '',
        description: '',
        devStatus: ''
      },
      rules: {
        name: [{ required: true, message: '请输入API名称', trigger: 'blur' }],
        method: [{ required: true, message: '请选择请求方法', trigger: 'change' }],
        path: [{ required: true, message: '请输入接口路径', trigger: 'blur' }]
      }
    })

    const editApiFormRef = ref(null)

    // 监听editMode的变化
    const editMode = computed(() => store.state.api.editMode)
    watch(editMode, (newValue) => {
      if (newValue === true) {
        // 重置状态，防止下次不触发
        store.commit('api/SET_EDIT_MODE', false)
      }
    })

    onMounted(() => {
      fetchApiDetail()
      loadDevStatusEnum()
      fetchEnvList()
    })

    async function fetchApiDetail() {
      try {
        loading.value = true
        // 获取API基本信息
        await store.dispatch('api/selectApi', props.apiId)
        apiInfo.value = store.state.api.currentApi || {}
        
        // 获取参数列表
        if (props.apiId) {
          try {
            // 改用参数树接口，避免空指针异常
            const paramsRes = await getParameterTree(props.apiId)
            
            // 分类参数
            headerParams.value = (paramsRes.data || []).filter(p => p.paramType === 'header')
            
            // 获取Path参数
            pathParams.value = (paramsRes.data || []).filter(p => p.paramType === 'path')
            
            // 过滤出顶级Body参数（没有父ID的）
            bodyParams.value = (paramsRes.data || [])
              .filter(p => p.paramType === 'body' && (!p.parentId || p.parentId === 0))
            
            // 过滤出顶级响应参数（没有父ID的）
            responseParams.value = (paramsRes.data || [])
              .filter(p => p.paramType === 'response' && (!p.parentId || p.parentId === 0))
            
            // 获取示例列表
            const examplesRes = await getExampleList(props.apiId)
            
            // 所有示例现在统一为请求示例，但包含响应内容
            apiRequestExamples.value = examplesRes.data || []
            
            // 如果没有示例，添加默认示例
            if (apiRequestExamples.value.length === 0) {
              // 根据接口参数生成默认示例
              
              // 创建默认请求内容
              let defaultRequestContent = '{}';
              if (bodyParams.value.length > 0) {
                defaultRequestContent = '{\n';
                bodyParams.value.forEach((param, index) => {
                  const comma = index < bodyParams.value.length - 1 ? ',' : '';
                  const value = getDefaultValueByType(param.type);
                  defaultRequestContent += `  "${param.name}": ${value}${comma}\n`;
                });
                defaultRequestContent += '}';
              }
              
              // 创建默认响应内容
              let defaultResponseContent = '{\n  "code": 200,\n  "msg": "ok",\n  "data": {';
              if (responseParams.value.length > 0) {
                responseParams.value.forEach((param, index) => {
                  const comma = index < responseParams.value.length - 1 ? ',' : '';
                  const value = getDefaultValueByType(param.type);
                  defaultResponseContent += `\n    "${param.name}": ${value}${comma}`;
                });
                defaultResponseContent += '\n  }\n}';
              } else {
                defaultResponseContent += '}\n}';
              }
              
              apiRequestExamples.value = [{
                name: '默认示例',
                content: defaultRequestContent,
                responseContent: defaultResponseContent,
                type: 'request',
                isDefault: 1,
                apiId: props.apiId
              }];
            }
            
            // 生成成对示例
            generatePairedExamples()
          } catch (e) {
            console.error('获取API参数失败', e)
          }
        }
      } catch (error) {
        console.error('获取API详情失败', error)
      } finally {
        loading.value = false
      }
    }
    
    // 生成成对示例
    function generatePairedExamples() {
      // 现在我们直接使用请求示例，因为它们已经包含响应内容
      pairedExamples.value = apiRequestExamples.value.map(example => ({
        request: example,
        response: { content: example.responseContent || '暂无响应示例' },
        isDefault: example.isDefault === 1
      }));
      
      // 如果没有示例，创建一个默认示例
      if (pairedExamples.value.length === 0) {
        let defaultRequestContent = '{\n  "username": "test"\n}';
        let defaultResponseContent = '{\n  "code": 200,\n  "msg": "ok",\n  "data": {}\n}';
        
        pairedExamples.value = [{
          request: {
            name: '默认示例',
            content: defaultRequestContent
          },
          response: {
            content: defaultResponseContent
          },
          isDefault: true
        }];
      }
      
      // 确保默认示例排在前面
      pairedExamples.value.sort((a, b) => {
        if (a.isDefault && !b.isDefault) return -1;
        if (!a.isDefault && b.isDefault) return 1;
        return 0;
      });
    }
    
    // 获取示例对的标签
    function getPairLabel(pair) {
      return `${pair.request.name}${pair.isDefault ? ' (默认)' : ''}`;
    }
    
    function editApi() {
      // 初始化编辑表单
      editApiDialog.value.form = {
        id: apiInfo.value.id,
        name: apiInfo.value.name,
        method: apiInfo.value.method,
        path: apiInfo.value.path,
        moduleName: apiInfo.value.moduleName,
        description: apiInfo.value.description,
        devStatus: apiInfo.value.devStatus
      }
      
      // 显示编辑对话框
      editApiDialog.value.visible = true
    }
    
    function closeEditApiDialog() {
      editApiDialog.value.visible = false
      nextTick(() => {
        if (editApiFormRef.value) {
          editApiFormRef.value.resetFields()
        }
      })
    }
    
    async function saveApiInfo() {
      if (!editApiFormRef.value) return
      
      try {
        await editApiFormRef.value.validate()
        
        loading.value = true
        const formData = { ...editApiDialog.value.form }
        
        // 调用API更新接口
        const result = await updateApi(formData.id, formData)
        
        if (result.code === 200) {
          ElMessage({
            message: '保存成功',
            type: 'success'
          })
          closeEditApiDialog()
          await fetchApiDetail() // 重新加载API详情
        } else {
          ElMessage({
            message: '保存失败：' + result.msg,
            type: 'error'
          })
        }
      } catch (error) {
        console.error('保存失败', error)
        ElMessage({
          message: '保存失败',
          type: 'error'
        })
      } finally {
        loading.value = false
      }
    }
    
    async function deleteApi() {
      try {
        // 确认删除
        if (!confirm('确定要删除该API吗？删除后将无法恢复！')) {
          return
        }
        
        loading.value = true
        const result = await request({
          url: `/api/info/${props.apiId}`, 
          method: 'delete'
        })
        
        if (result.code === 200) {
          alert('删除成功')
          emit('deleted')
        } else {
          alert('删除失败：' + result.msg)
        }
      } catch (error) {
        console.error('删除失败', error)
        alert('删除失败')
      } finally {
        loading.value = false
      }
    }
    
    // 参数相关方法
    function openAddParamDialog(type, parentId = null) {
      paramDialog.value.isEdit = false
      paramDialog.value.allowedTypes = [type]
      
      // 如果parentId是以temp-开头的字符串，表示是临时ID，设为null
      if (typeof parentId === 'string' && parentId.startsWith('temp-')) {
        parentId = null
      }
      
      paramDialog.value.parentId = parentId
      paramDialog.value.form = {
        name: '',
        type: 'String',
        paramType: type,
        description: '',
        required: 0,
        parentId: parentId
      }
      paramDialog.value.visible = true
    }
    
    function openEditParamDialog(param) {
      paramDialog.value.isEdit = true
      paramDialog.value.allowedTypes = [param.paramType]
      paramDialog.value.form = {
        id: param.id,
        name: param.name,
        type: param.type,
        paramType: param.paramType,
        description: param.description,
        required: param.required,
        parentId: param.parentId
      }
      paramDialog.value.visible = true
    }
    
    function closeParamDialog() {
      paramDialog.value.visible = false
      nextTick(() => {
        if (paramFormRef.value) {
          paramFormRef.value.resetFields()
        }
      })
    }
    
    const paramFormRef = ref(null)
    
    async function saveParameter() {
      if (!paramFormRef.value) return
      
      try {
        await paramFormRef.value.validate()
        
        loading.value = true
        const formData = {
          ...paramDialog.value.form,
          apiId: props.apiId
        }
        
        // 处理临时ID和parentId
        if (formData.id && typeof formData.id === 'string' && formData.id.startsWith('temp-')) {
          delete formData.id  // 删除临时ID，让后端生成新ID
        }
        
        // 确保parentId为数字或null
        if (formData.parentId && typeof formData.parentId === 'string' && formData.parentId.startsWith('temp-')) {
          formData.parentId = null  // 将临时ID的parentId设置为null
        }
        
        let result
        if (paramDialog.value.isEdit) {
          // 编辑参数，使用PUT /api/parameter/{id}
          result = await updateParameter(formData.id, formData)
        } else {
          // 创建参数，使用新的单个参数创建接口
          result = await createParameter(formData)
        }
        
        if (result.code === 200) {
          alert('保存成功')
          closeParamDialog()
          await fetchApiDetail()
        } else {
          alert('保存失败：' + result.msg)
        }
      } catch (error) {
        console.error('保存失败', error)
        alert('保存失败')
      } finally {
        loading.value = false
      }
    }
    
    async function deleteParam(param) {
      try {
        // 确认删除
        if (!confirm('确定要删除该参数吗？')) {
          return
        }
        
        loading.value = true
        const result = await deleteParameter(param.id)
        
        if (result.code === 200) {
          alert('删除成功')
          await fetchApiDetail()
        } else {
          alert('删除失败：' + result.msg)
        }
      } catch (error) {
        console.error('删除失败', error)
        alert('删除失败')
      } finally {
        loading.value = false
      }
    }
    
    // 示例相关方法
    function openExampleDialog() {
      exampleDialog.value.visible = true
      exampleDialog.value.newExampleCount = 0
      
      // 获取当前API的所有示例
      const examples = apiRequestExamples.value
      
      if (examples.length > 0) {
        // 转换现有示例为新格式
        exampleDialog.value.pairs = examples.map(example => ({
          id: example.id,
          name: example.name,
          requestContent: example.content,
          responseContent: example.responseContent || '',
          type: example.type,
          isDefault: example.isDefault
        }))
      } else {
        // 创建默认示例
        exampleDialog.value.pairs = [{
          tempId: 'temp_1',
          name: '默认示例',
          requestContent: '{\n  \n}',
          responseContent: '{\n  \n}',
          type: 'request',
          isDefault: 1
        }]
      }
      
      if (exampleDialog.value.pairs.length > 0) {
        exampleDialog.value.activeTab = String(
          exampleDialog.value.pairs[0].id || exampleDialog.value.pairs[0].tempId
        )
      }
    }
    
    function closeExampleDialog() {
      exampleDialog.value.visible = false
      exampleDialog.value.pairs = []
    }
    
    function addNewExamplePair() {
      exampleDialog.value.newExampleCount++
      const tempId = `temp_${Date.now()}`
      const newPair = {
        tempId,
        name: `示例 ${exampleDialog.value.pairs.length + 1}`,
        requestContent: '{\n  \n}',
        responseContent: '{\n  \n}',
        type: exampleDialog.value.type,
        isDefault: exampleDialog.value.pairs.length === 0 ? 1 : 0
      }
      
      exampleDialog.value.pairs.push(newPair)
      exampleDialog.value.activeTab = tempId
    }
    
    function removeExamplePair(tabName) {
      const index = exampleDialog.value.pairs.findIndex(
        item => String(item.id || item.tempId) === tabName
      )
      
      if (index !== -1) {
        const removedPair = exampleDialog.value.pairs[index]
        exampleDialog.value.pairs.splice(index, 1)
        
        // 如果删除的是默认示例，则设置第一个为默认
        if (removedPair.isDefault === 1 && exampleDialog.value.pairs.length > 0) {
          exampleDialog.value.pairs[0].isDefault = 1
        }
        
        // 更新激活的标签
        if (exampleDialog.value.pairs.length > 0) {
          exampleDialog.value.activeTab = String(
            exampleDialog.value.pairs[0].id || exampleDialog.value.pairs[0].tempId
          )
        }
      }
    }
    
    async function saveExamplePairs() {
      try {
        loading.value = true
        
        // 创建要提交的数据
        const data = exampleDialog.value.pairs.map(pair => {
          // 创建要提交的对象
          const example = {
            name: pair.name,
            content: pair.requestContent,
            responseContent: pair.responseContent,
            type: 'request', // 统一使用request类型
            isDefault: pair.isDefault,
            apiId: props.apiId
          };
          
          // 只有当ID是数字值时才添加ID字段
          if (pair.id && !isNaN(parseInt(pair.id)) && String(parseInt(pair.id)) === String(pair.id)) {
            example.id = pair.id;
          }
          
          return example;
        })
        
        const result = await batchSaveExamples(data)
        
        if (result.code === 200) {
          alert('保存成功')
          closeExampleDialog()
          await fetchApiDetail()
        } else {
          alert('保存失败：' + result.msg)
        }
      } catch (error) {
        console.error('保存失败', error)
        alert('保存失败')
      } finally {
        loading.value = false
      }
    }
    
    function getStatusClass(method) {
      const map = {
        GET: 'success',
        POST: 'primary',
        PUT: 'warning',
        DELETE: 'danger',
        PATCH: 'info'
      }
      return map[method?.toUpperCase()] || 'info'
    }
    
    function formatDateTime(timestamp) {
      if (!timestamp) return '未设置';
      
      try {
        const date = new Date(timestamp);
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) return timestamp;
        
        // 格式化为 yyyy-MM-dd HH:mm:ss
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (e) {
        console.error('日期格式化错误', e);
        return timestamp;
      }
    }
    
    // 复制文本到剪贴板
    function copyText(text) {
      if (!text) return;
      
      try {
        navigator.clipboard.writeText(text).then(() => {
          // 使用 Element Plus 的消息提示
          ElMessage({
            message: '复制成功，已复制到剪贴板',
            type: 'success',
            duration: 1500
          });
        });
      } catch (err) {
        console.error('复制失败', err);
        // 回退方法
        const textarea = document.createElement('textarea');
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
        
        ElMessage({
          message: '复制成功，已复制到剪贴板',
          type: 'success',
          duration: 1500
        });
      }
    }
    
    // 根据类型获取默认值
    function getDefaultValueByType(type) {
      switch (type) {
        case 'String':
          return '"示例文本"';
        case 'Integer':
        case 'Long':
          return '100';
        case 'Float':
        case 'Double':
          return '10.5';
        case 'Boolean':
          return 'true';
        case 'Date':
          return '"2023-01-01 12:00:00"';
        case 'Array':
          return '[]';
        case 'Object':
          return '{}';
        default:
          return '""';
      }
    }
    
    // 获取开发状态枚举
    async function loadDevStatusEnum() {
      try {
        const res = await getApiDevStatusEnum()
        if (res.code === 200 && res.data) {
          // 构建状态映射表
          const statusMap = {}
          res.data.forEach(item => {
            statusMap[item.code] = item.name
          })
          devStatusMap.value = statusMap
        }
      } catch (error) {
        console.error('获取API状态枚举失败', error)
      }
    }
    
    // 获取显示友好的状态名称
    function getDevStatusName(statusCode) {
      return devStatusMap.value[statusCode] || statusCode || '未设置'
    }
    
    // 打开状态更新对话框
    function openStatusUpdateDialog() {
      statusUpdateDialogVisible.value = true
    }
    
    // 状态更新成功处理函数
    function handleStatusUpdated() {
      // 重新加载API详情
      fetchApiDetail()
      // 刷新历史记录
      if (historyRef.value) {
        historyRef.value.fetchHistoryList()
      }
    }
    
    // 获取用户名
    const getUserName = async (userId) => {
      if (!userId) return '--'
      const name = await store.dispatch('user/getUserName', userId)
      return name || userId
    }

    // 计算属性
    const creatorName = computed(() => {
      return store.getters.getUserNameById(apiInfo.value?.createBy)
    })

    // 计算完整URL
    const fullUrl = computed(() => {
      if (!currentBaseUrl.value || !apiInfo.value?.path) return ''
      return currentBaseUrl.value + apiInfo.value.path
    })

    // 扁平化Body参数
    const flatBodyParams = computed(() => {
      return flattenParams(bodyParams.value)
    })

    // 扁平化参数树
    function flattenParams(params, level = 0) {
      const result = []
      params.forEach(param => {
        result.push({ ...param, level })
        if (param.children && param.children.length > 0) {
          result.push(...flattenParams(param.children, level + 1))
        }
      })
      return result
    }

    // 生成JSON示例
    function generateJsonExample() {
      const example = {}

      function buildExample(params, target) {
        params.forEach(param => {
          if (param.children && param.children.length > 0) {
            if (param.type === 'Array') {
              target[param.name] = [{}]
              buildExample(param.children, target[param.name][0])
            } else {
              target[param.name] = {}
              buildExample(param.children, target[param.name])
            }
          } else {
            target[param.name] = getDefaultValueByType(param.type)
          }
        })
      }

      buildExample(bodyParams.value, example)
      return JSON.stringify(example, null, 2)
    }

    // 获取方法类型样式
    function getMethodType(method) {
      switch (method) {
        case 'GET': return 'success'
        case 'POST': return 'primary'
        case 'PUT': return 'warning'
        case 'DELETE': return 'danger'
        case 'PATCH': return 'info'
        default: return 'info'
      }
    }

    // 格式化日期
    function formatDate(dateStr) {
      if (!dateStr) return '-'
      const date = new Date(dateStr)
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN')
    }

    // 获取环境列表
    async function fetchEnvList() {
      try {
        const res = await getEnvList()
        if (res.code === 200) {
          envList.value = res.data || []
          if (envList.value.length > 0) {
            selectedEnv.value = envList.value[0].id
            onEnvChange(selectedEnv.value)
          }
        }
      } catch (err) {
        console.error('获取环境列表失败:', err)
      }
    }

    // 环境变化
    function onEnvChange(envId) {
      const env = envList.value.find(e => e.id === envId)
      if (env) {
        currentBaseUrl.value = env.envUrl
      }
    }

    // 发送请求
    async function sendRequest() {
      if (!selectedEnv.value) {
        ElMessage.warning('请先选择环境')
        return
      }

      if (!apiInfo.value?.path) {
        ElMessage.warning('接口路径不能为空')
        return
      }

      try {
        requesting.value = true

        const config = {
          method: apiInfo.value.method.toLowerCase(),
          url: currentBaseUrl.value + apiInfo.value.path,
          headers: {
            'Content-Type': 'application/json'
          }
        }

        await axios(config)
        ElMessage.success('请求发送成功')
      } catch (err) {
        ElMessage.error('请求失败: ' + err.message)
      } finally {
        requesting.value = false
      }
    }

    return {
      apiInfo,
      headerParams,
      pathParams,
      bodyParams,
      responseParams,
      apiRequestExamples,
      pairedExamples,
      loading,
      paramDialog,
      exampleDialog,
      paramFormRef,
      editApiDialog,
      editApiFormRef,
      editApi,
      closeEditApiDialog,
      saveApiInfo,
      deleteApi,
      openAddParamDialog,
      openEditParamDialog,
      closeParamDialog,
      saveParameter,
      deleteParam,
      openExampleDialog,
      closeExampleDialog,
      addNewExamplePair,
      removeExamplePair,
      saveExamplePairs,
      getStatusClass,
      getPairLabel,
      formatDateTime,
      copyText,
      getDevStatusName,
      getUserName,
      creatorName,
      moduleName,
      historyRef,
      statusUpdateDialogVisible,
      openStatusUpdateDialog,
      handleStatusUpdated,
      activeTab,
      // 新增的方法和数据
      envList,
      selectedEnv,
      currentBaseUrl,
      fullUrl,
      requesting,
      flatBodyParams,
      generateJsonExample,
      getMethodType,
      formatDate,
      onEnvChange,
      sendRequest,
      fetchEnvList
    }
  }
}
</script>

<style lang="scss" scoped>
.api-detail {
  background: #fff;
  min-height: 100vh;

  /* 顶部操作栏 */
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid #e8e8e8;
    background: #fafafa;
  }

  .header-tabs {
    display: flex;
    gap: 24px;
  }

  .tab-item {
    padding: 8px 16px;
    cursor: pointer;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;

    &.active {
      color: #1890ff;
      border-bottom-color: #1890ff;
    }
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }

  /* 接口基本信息 */
  .api-info-section {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  .api-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .api-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
  }

  .api-method {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .method-tag {
    font-weight: 600;
  }

  .api-path {
    font-family: monospace;
    color: #1890ff;
    background: #f0f9ff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
  }

  .api-meta {
    display: flex;
    gap: 24px;
    color: #666;
    font-size: 14px;
  }

  .meta-item {
    display: flex;
    align-items: center;
  }

  /* Mock 环境配置 */
  .mock-section {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  .mock-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
  }

  .mock-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .mock-dropdown {
    cursor: pointer;
    color: #1890ff;
    font-size: 14px;
  }

  .mock-config {
    background: #fafafa;
    border-radius: 4px;
    overflow: hidden;
  }

  .config-row {
    display: grid;
    grid-template-columns: 120px 100px 1fr 100px;
    gap: 16px;
    padding: 12px 16px;
    align-items: center;

    &:first-child {
      background: #f0f0f0;
      font-weight: 600;
      color: #666;
      font-size: 14px;
    }
  }

  .config-data {
    border-top: 1px solid #e8e8e8;
  }

  .url-display {
    font-family: monospace;
    color: #1890ff;
    background: #f0f9ff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
  }
  /* 内容区域 */
  .content-section {
    padding: 20px;
  }

  /* 请求参数配置 */
  .params-section {
    .section-title {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .param-group {
      margin-bottom: 24px;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      overflow: hidden;
    }

    .param-group-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #fafafa;
      border-bottom: 1px solid #e8e8e8;
    }

    .group-title {
      font-weight: 600;
      color: #333;
    }

    .body-type-selector {
      display: flex;
      align-items: center;
    }

    .body-type-label {
      color: #666;
      font-size: 12px;
      background: #f0f0f0;
      padding: 4px 8px;
      border-radius: 4px;
    }

    .param-list {
      padding: 16px;
    }

    .param-item {
      margin-bottom: 16px;
      padding: 12px;
      background: #f9f9f9;
      border-radius: 4px;
      border-left: 3px solid #1890ff;
    }

    .param-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
    }

    .param-name {
      font-weight: 600;
      color: #333;

      &.required {
        color: #1890ff;
      }
    }

    .param-type {
      color: #666;
      font-size: 12px;
      background: #f0f0f0;
      padding: 2px 6px;
      border-radius: 2px;
    }

    .param-required-badge {
      background: #ff4d4f;
      color: white;
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 2px;
    }

    .param-desc {
      color: #666;
      font-size: 12px;
      margin-bottom: 8px;
      line-height: 1.4;
    }

    .param-actions {
      display: flex;
      gap: 8px;
    }

    /* Body 参数特殊样式 */
    .body-content {
      display: flex;
      gap: 20px;
      padding: 16px;
    }

    .body-left {
      flex: 1;
    }

    .body-right {
      flex: 1;
    }

    .param-tree {
      background: #f9f9f9;
      border-radius: 4px;
      padding: 16px;
    }

    .tree-item {
      margin-bottom: 12px;
      padding: 8px;
      background: white;
      border-radius: 4px;
      border-left: 3px solid #1890ff;
    }

    .tree-item-content {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
    }

    .json-example {
      background: #f9f9f9;
      border-radius: 4px;
      overflow: hidden;
    }

    .example-header {
      padding: 12px 16px;
      background: #f0f0f0;
      font-weight: 600;
      color: #333;
      border-bottom: 1px solid #e8e8e8;
    }

    .json-content {
      padding: 16px;
    }

    .json-code {
      background: white;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      padding: 16px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      line-height: 1.5;
      color: #333;
      overflow-x: auto;
    }
  }

  /* 编辑标签页样式 */
  .edit-content {
    .edit-actions {
      margin-bottom: 24px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 4px;
      display: flex;
      gap: 12px;
    }

    .param-management h3 {
      margin: 0 0 20px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .param-table-section {
      margin-bottom: 32px;

      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h4 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }
    }

    .example-management {
      margin-top: 32px;
    }
  }
  
  .section {
    margin-bottom: 30px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .section-header {
      padding: 12px 20px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
  
  .info-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 20px;
    padding: 20px;
    
    .info-item {
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 4px;
      
      .label {
        font-weight: bold;
        margin-bottom: 5px;
        color: #606266;
      }
      
      .value {
        color: #333;
        word-break: break-all;
      }
      
      &.description {
        grid-column: span 2;
      }
    }
  }
  
  .param-section {
    padding: 20px;
    
    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      h3 {
        margin: 0;
      }
      
      .section-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .empty-data {
      padding: 20px;
      text-align: center;
      color: #909399;
      background-color: #f8f8f8;
      border-radius: 4px;
      display: none; /* 隐藏No Data提示 */
    }
    
    .code-block {
      padding: 15px;
      background-color: #f6f8fa;
      border-radius: 4px;
      white-space: pre-wrap;
      word-break: break-word;
      font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
      cursor: pointer;
      transition: background-color 0.2s;
      max-width: 100%;
      overflow-x: hidden;
      
      &:hover {
        background-color: #f0f2f5;
      }
    }
    
    .example-pair {
      display: flex;
      gap: 20px;
      
      .example-item {
        flex: 1;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        
        .example-header {
          padding: 10px;
          background-color: #f5f7fa;
          border-bottom: 1px solid #ebeef5;
          font-weight: bold;
          text-align: center;
        }
        
        .code-block {
          border-radius: 0 0 4px 4px;
          margin: 0;
          height: 100%;
          min-height: 150px;
        }
      }
    }
    
    .example-edit-pair {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      
      .example-edit-item {
        flex: 1;
        
        .example-edit-header {
          padding: 10px;
          background-color: #f5f7fa;
          border: 1px solid #ebeef5;
          border-bottom: none;
          border-radius: 4px 4px 0 0;
          font-weight: bold;
          text-align: center;
        }
        
        .el-textarea {
          margin-top: -1px;
          
          :deep(.el-textarea__inner) {
            border-radius: 0 0 4px 4px;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            white-space: pre-wrap;
            word-break: break-word;
          }
        }
      }
    }
  }
  
  .dialog-action {
    margin-top: 15px;
    text-align: center;
  }
}

.copy-wrapper {
  display: flex;
  align-items: center;
}
.copy-wrapper span {
  flex: 1;
}
.path-text {
  cursor: pointer;
  color: #606266;
  transition: all 0.2s;
  position: relative;
}
.path-text:hover {
  text-decoration: underline;
}
.copy-icon {
  cursor: pointer;
  margin-left: 5px;
  color: #409EFF;
  transition: all 0.3s;
}
.copy-icon:hover {
  opacity: 0.8;
  transform: scale(1.1);
}

.code-block-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
}

.example-copy-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 18px;
  z-index: 10;
  background-color: #f6f8fa;
  border-radius: 4px;
  padding: 4px;
}

.button-group {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.el-button--mini {
  padding: 3px 6px;
  height: 24px;
  font-size: 11px;
  line-height: 1;
  margin: 2px 0;
}

.el-button--mini + .el-button--mini {
  margin-left: 0;
}

// 各种按钮的最大宽度
.el-button--success.el-button--mini {
  max-width: 75px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.el-button--primary.el-button--mini, 
.el-button--danger.el-button--mini {
  max-width: 50px;
}
</style> 