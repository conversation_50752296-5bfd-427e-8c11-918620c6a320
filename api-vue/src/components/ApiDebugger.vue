<template>
  <div class="api-debugger">
    <!-- 顶部操作栏 -->
    <div class="debugger-header">
      <div class="header-tabs">
        <div class="tab-item active">文档</div>
        <div class="tab-item">修改文档</div>
        <div class="tab-item">运行</div>
        <div class="tab-item">高级 Mock</div>
      </div>
      <div class="header-actions">
        <el-button type="primary" size="small" icon="Share">分享</el-button>
        <el-button size="small" icon="Code">生成代码</el-button>
      </div>
    </div>

    <!-- 接口基本信息 -->
    <div class="api-info-section">
      <h2 class="api-title">{{ apiInfo.name || '接口调试' }}</h2>
      <div class="api-meta">
        <span class="meta-item">创建时间: {{ formatDate(apiInfo.createTime) }}</span>
        <span class="meta-item">修改时间: {{ formatDate(apiInfo.updateTime) }}</span>
        <span class="meta-item">创建者: {{ apiInfo.createBy }}</span>
        <span class="meta-item">目录: {{ apiInfo.groupName || '默认' }}</span>
      </div>
    </div>

    <!-- Mock 环境选择 -->
    <div class="mock-section">
      <div class="mock-header">
        <span class="mock-title">Mock</span>
        <el-dropdown>
          <span class="mock-dropdown">
            本地 Mock <i class="el-icon-arrow-down"></i>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>本地 Mock</el-dropdown-item>
              <el-dropdown-item>云端 Mock</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      
      <div class="mock-config">
        <div class="config-row">
          <div class="config-label">名称</div>
          <div class="config-label">来源</div>
          <div class="config-label">URL / 参数</div>
          <div class="config-actions"></div>
        </div>
        <div class="config-row config-data">
          <div class="config-value">
            <el-select v-model="selectedEnv" placeholder="选择环境" @change="onEnvChange" size="small">
              <el-option
                v-for="env in envList"
                :key="env.id"
                :label="env.envName"
                :value="env.id"
              />
            </el-select>
          </div>
          <div class="config-value">接口地址</div>
          <div class="config-value url-display">{{ fullUrl }}</div>
          <div class="config-actions">
            <el-button type="primary" size="small" @click="sendRequest" :loading="requesting">
              {{ requesting ? '发送中' : '保存' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 请求参数配置 -->
    <div class="params-section">
      <h3 class="section-title">请求参数</h3>
      
      <!-- Header 参数 -->
      <div v-if="headerParams.length > 0" class="param-group">
        <div class="param-group-header">
          <span class="group-title">Header 参数</span>
        </div>
        <div class="param-list">
          <div 
            v-for="param in headerParams" 
            :key="param.id"
            class="param-item"
          >
            <div class="param-info">
              <span class="param-name" :class="{ required: param.required }">{{ param.name }}</span>
              <span class="param-type">{{ param.type }}</span>
              <span class="param-required-badge" v-if="param.required">必需</span>
            </div>
            <div class="param-input">
              <el-input 
                v-model="requestData.headers[param.name]" 
                :placeholder="param.description || '请输入' + param.name"
                size="small"
              />
            </div>
            <div class="param-desc" v-if="param.description">{{ param.description }}</div>
          </div>
        </div>
      </div>

      <!-- Body 参数 -->
      <div v-if="bodyParams.length > 0" class="param-group">
        <div class="param-group-header">
          <span class="group-title">Body 参数</span>
          <div class="body-type-selector">
            <el-radio-group v-model="bodyType" size="small">
              <el-radio-button label="json">application/json</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        
        <div class="body-content">
          <div class="body-left">
            <!-- 参数列表 -->
            <div class="param-tree">
              <div 
                v-for="param in flatBodyParams" 
                :key="param.id"
                class="tree-item"
                :style="{ paddingLeft: (param.level * 20) + 'px' }"
              >
                <div class="tree-item-content">
                  <span class="param-name" :class="{ required: param.required }">{{ param.name }}</span>
                  <span class="param-type">{{ param.type }}</span>
                  <span class="param-required-badge" v-if="param.required">必需</span>
                </div>
                <div class="param-desc" v-if="param.description">{{ param.description }}</div>
              </div>
            </div>
          </div>
          
          <div class="body-right">
            <!-- JSON 示例 -->
            <div class="json-example">
              <div class="example-header">示例</div>
              <div class="json-content">
                <pre class="json-code">{{ generateJsonExample() }}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 响应结果区域 -->
    <div class="response-section" v-if="response || error">
      <h3 class="section-title">响应结果</h3>
      
      <div v-if="response" class="response-content">
        <div class="response-header">
          <div class="response-status">
            <el-tag 
              :type="response.success ? 'success' : 'danger'"
              size="large"
            >
              {{ response.status }} {{ response.statusText }}
            </el-tag>
            <span class="response-time">{{ response.duration }}ms</span>
          </div>
        </div>
        
        <div class="response-body">
          <div class="response-left">
            <div class="response-data">
              <div class="data-header">响应数据</div>
              <pre class="json-viewer">{{ formatJson(response.data) }}</pre>
            </div>
          </div>
          
          <div class="response-right" v-if="responseParams.length > 0">
            <div class="response-description">
              <div class="desc-header">参数说明</div>
              <div class="param-descriptions">
                <div 
                  v-for="param in flatResponseParams" 
                  :key="param.id"
                  class="param-desc-item"
                  :style="{ paddingLeft: (param.level * 20) + 'px' }"
                >
                  <div class="desc-item-content">
                    <span class="param-name">{{ param.name }}</span>
                    <span class="param-type">({{ param.type }})</span>
                  </div>
                  <div class="param-desc">{{ param.description || '-' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 错误信息 -->
      <div v-if="error" class="error-content">
        <el-alert
          :title="error.message"
          type="error"
          :description="error.details"
          show-icon
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getEnvList } from '@/api/env'
import axios from 'axios'

export default {
  name: 'ApiDebugger',
  props: {
    apiInfo: {
      type: Object,
      required: true
    },
    headerParams: {
      type: Array,
      default: () => []
    },
    pathParams: {
      type: Array,
      default: () => []
    },
    bodyParams: {
      type: Array,
      default: () => []
    },
    responseParams: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const envList = ref([])
    const selectedEnv = ref('')
    const currentBaseUrl = ref('')
    const bodyType = ref('json')
    const requesting = ref(false)
    const response = ref(null)
    const error = ref(null)

    const requestData = ref({
      headers: {},
      pathParams: {},
      body: {},
      bodyJson: ''
    })

    // 计算完整URL
    const fullUrl = computed(() => {
      if (!currentBaseUrl.value || !props.apiInfo.path) return ''
      let url = currentBaseUrl.value + props.apiInfo.path
      
      // 替换路径参数
      Object.keys(requestData.value.pathParams).forEach(key => {
        const value = requestData.value.pathParams[key]
        if (value) {
          url = url.replace(`{${key}}`, value).replace(`:${key}`, value)
        }
      })
      
      return url
    })

    // 扁平化参数列表
    const flatBodyParams = computed(() => {
      return flattenParams(props.bodyParams)
    })

    const flatResponseParams = computed(() => {
      return flattenParams(props.responseParams)
    })

    // 扁平化参数树
    function flattenParams(params, level = 0) {
      const result = []
      params.forEach(param => {
        result.push({ ...param, level })
        if (param.children && param.children.length > 0) {
          result.push(...flattenParams(param.children, level + 1))
        }
      })
      return result
    }

    // 获取环境列表
    async function fetchEnvList() {
      try {
        const res = await getEnvList()
        if (res.code === 200) {
          envList.value = res.data || []
          // 默认选择第一个环境
          if (envList.value.length > 0) {
            selectedEnv.value = envList.value[0].id
            onEnvChange(selectedEnv.value)
          }
        }
      } catch (err) {
        console.error('获取环境列表失败:', err)
        ElMessage.error('获取环境列表失败')
      }
    }

    // 环境变化
    function onEnvChange(envId) {
      const env = envList.value.find(e => e.id === envId)
      if (env) {
        currentBaseUrl.value = env.envUrl
      }
    }

    // 发送请求
    async function sendRequest() {
      if (!selectedEnv.value) {
        ElMessage.warning('请先选择环境')
        return
      }

      if (!props.apiInfo.path) {
        ElMessage.warning('接口路径不能为空')
        return
      }

      const startTime = Date.now()

      try {
        requesting.value = true
        error.value = null

        // 构建请求URL
        let url = currentBaseUrl.value + props.apiInfo.path

        // 替换路径参数
        Object.keys(requestData.value.pathParams).forEach(key => {
          const value = requestData.value.pathParams[key]
          if (value) {
            url = url.replace(`{${key}}`, value).replace(`:${key}`, value)
          }
        })

        // 构建请求配置
        const config = {
          method: props.apiInfo.method.toLowerCase(),
          url: url,
          headers: {
            'Content-Type': 'application/json',
            ...requestData.value.headers
          }
        }

        // 添加请求体
        if (['post', 'put', 'patch'].includes(config.method)) {
          if (bodyType.value === 'json' && requestData.value.bodyJson) {
            try {
              config.data = JSON.parse(requestData.value.bodyJson)
            } catch (e) {
              ElMessage.error('JSON 格式错误')
              return
            }
          } else if (bodyType.value === 'form') {
            config.data = requestData.value.body
          }
        }

        // 发送请求
        const res = await axios(config)
        const endTime = Date.now()

        response.value = {
          success: true,
          status: res.status,
          statusText: res.statusText,
          headers: res.headers,
          data: res.data,
          duration: endTime - startTime
        }

        ElMessage.success('请求发送成功')
      } catch (err) {
        const endTime = Date.now()

        if (err.response) {
          response.value = {
            success: false,
            status: err.response.status,
            statusText: err.response.statusText,
            headers: err.response.headers,
            data: err.response.data,
            duration: endTime - startTime
          }
        } else {
          error.value = {
            message: '请求失败',
            details: err.message
          }
        }
      } finally {
        requesting.value = false
      }
    }

    // 格式化JSON
    function formatJson(obj) {
      try {
        return JSON.stringify(obj, null, 2)
      } catch (e) {
        return obj
      }
    }

    // 格式化日期
    function formatDate(dateStr) {
      if (!dateStr) return '-'
      const date = new Date(dateStr)
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN')
    }

    // 生成JSON示例
    function generateJsonExample() {
      const example = {}

      function buildExample(params, target) {
        params.forEach(param => {
          if (param.children && param.children.length > 0) {
            if (param.type === 'Array') {
              target[param.name] = [{}]
              buildExample(param.children, target[param.name][0])
            } else {
              target[param.name] = {}
              buildExample(param.children, target[param.name])
            }
          } else {
            target[param.name] = getDefaultValueByType(param.type)
          }
        })
      }

      buildExample(props.bodyParams, example)
      return JSON.stringify(example, null, 2)
    }

    // 根据类型获取默认值
    function getDefaultValueByType(type) {
      switch (type) {
        case 'String': return ''
        case 'Integer':
        case 'Long':
        case 'Float':
        case 'Double': return 0
        case 'Boolean': return false
        case 'Array': return []
        case 'Object': return {}
        default: return ''
      }
    }

    // 监听API变化，重置请求数据
    watch(() => props.apiInfo, () => {
      requestData.value = {
        headers: {},
        pathParams: {},
        body: {},
        bodyJson: ''
      }
      response.value = null
      error.value = null
    })

    onMounted(() => {
      fetchEnvList()
    })

    return {
      envList,
      selectedEnv,
      currentBaseUrl,
      fullUrl,
      bodyType,
      requesting,
      requestData,
      response,
      error,
      flatBodyParams,
      flatResponseParams,
      onEnvChange,
      sendRequest,
      formatJson,
      formatDate,
      generateJsonExample
    }
  }
}
</script>

<style scoped>
.api-debugger {
  background: #fff;
  min-height: 100vh;
}

/* 顶部操作栏 */
.debugger-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.header-tabs {
  display: flex;
  gap: 24px;
}

.tab-item {
  padding: 8px 16px;
  cursor: pointer;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.tab-item.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 接口基本信息 */
.api-info-section {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.api-title {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.api-meta {
  display: flex;
  gap: 24px;
  color: #666;
  font-size: 14px;
}

.meta-item {
  display: flex;
  align-items: center;
}

/* Mock 环境配置 */
.mock-section {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.mock-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.mock-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.mock-dropdown {
  cursor: pointer;
  color: #1890ff;
  font-size: 14px;
}

.mock-config {
  background: #fafafa;
  border-radius: 4px;
  overflow: hidden;
}

.config-row {
  display: grid;
  grid-template-columns: 120px 100px 1fr 100px;
  gap: 16px;
  padding: 12px 16px;
  align-items: center;
}

.config-row:first-child {
  background: #f0f0f0;
  font-weight: 600;
  color: #666;
  font-size: 14px;
}

.config-data {
  border-top: 1px solid #e8e8e8;
}

.url-display {
  font-family: monospace;
  color: #1890ff;
  background: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* 请求参数配置 */
.params-section {
  padding: 20px;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.param-group {
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.param-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.group-title {
  font-weight: 600;
  color: #333;
}

.body-type-selector {
  display: flex;
  align-items: center;
}

.param-list {
  padding: 16px;
}

.param-item {
  margin-bottom: 16px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.param-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.param-name {
  font-weight: 600;
  color: #333;
}

.param-name.required {
  color: #1890ff;
}

.param-type {
  color: #666;
  font-size: 12px;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 2px;
}

.param-required-badge {
  background: #ff4d4f;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 2px;
}

.param-desc {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}

/* Body 参数特殊样式 */
.body-content {
  display: flex;
  gap: 20px;
  padding: 16px;
}

.body-left {
  flex: 1;
}

.body-right {
  flex: 1;
}

.param-tree {
  background: #f9f9f9;
  border-radius: 4px;
  padding: 16px;
}

.tree-item {
  margin-bottom: 12px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.tree-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.json-example {
  background: #f9f9f9;
  border-radius: 4px;
  overflow: hidden;
}

.example-header {
  padding: 12px 16px;
  background: #f0f0f0;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e8e8e8;
}

.json-content {
  padding: 16px;
}

.json-code {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #333;
  overflow-x: auto;
}

/* 响应结果样式 */
.response-section {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.response-content {
  margin-top: 16px;
}

.response-header {
  margin-bottom: 16px;
}

.response-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.response-time {
  color: #666;
  font-size: 14px;
}

.response-body {
  display: flex;
  gap: 20px;
}

.response-left {
  flex: 1;
}

.response-right {
  flex: 0 0 300px;
}

.response-data {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.data-header {
  padding: 12px 16px;
  background: #fafafa;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e8e8e8;
}

.json-viewer {
  background: white;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #333;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 400px;
  overflow-y: auto;
}

.response-description {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.desc-header {
  padding: 12px 16px;
  background: #fafafa;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e8e8e8;
}

.param-descriptions {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.param-desc-item {
  margin-bottom: 12px;
  padding: 8px;
  background: #f9f9f9;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.desc-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.error-content {
  margin-top: 16px;
}
</style>
