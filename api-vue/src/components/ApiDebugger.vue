<template>
  <div class="api-debugger">
    <!-- 请求配置区域 -->
    <div class="request-section">
      <!-- 请求行 -->
      <div class="request-line">
        <div class="method-selector">
          <el-tag
            :type="getMethodType(apiInfo.method)"
            size="large"
            class="method-tag"
          >
            {{ apiInfo.method }}
          </el-tag>
        </div>

        <div class="url-input">
          <el-input
            v-model="fullUrl"
            readonly
            class="url-field"
            placeholder="请求URL"
          >
            <template #prepend>
              <el-select
                v-model="selectedEnv"
                placeholder="选择环境"
                @change="onEnvChange"
                style="width: 120px"
              >
                <el-option
                  v-for="env in envList"
                  :key="env.id"
                  :label="env.envName"
                  :value="env.id"
                />
              </el-select>
            </template>
          </el-input>
        </div>

        <div class="send-button">
          <el-button
            type="primary"
            size="large"
            @click="sendRequest"
            :loading="requesting"
            :disabled="!selectedEnv"
            class="send-btn"
          >
            {{ requesting ? '发送中...' : '发送' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 参数配置标签页 -->
    <div class="params-section">
      <el-tabs v-model="activeParamTab" class="param-tabs">
        <!-- Header 参数 -->
        <el-tab-pane label="Headers" name="headers" v-if="headerParams.length > 0">
          <div class="param-table">
            <div class="param-table-header">
              <div class="param-col-key">参数名</div>
              <div class="param-col-value">参数值</div>
              <div class="param-col-desc">说明</div>
            </div>
            <div class="param-table-body">
              <div
                v-for="param in headerParams"
                :key="param.id"
                class="param-row"
              >
                <div class="param-col-key">
                  <span class="param-name">{{ param.name }}</span>
                  <span class="param-required" v-if="param.required">*</span>
                  <span class="param-type">({{ param.type }})</span>
                </div>
                <div class="param-col-value">
                  <el-input
                    v-model="requestData.headers[param.name]"
                    :placeholder="param.description || '请输入' + param.name"
                    size="small"
                  />
                </div>
                <div class="param-col-desc">{{ param.description || '-' }}</div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- Path 参数 -->
        <el-tab-pane label="Path Params" name="path" v-if="pathParams.length > 0">
          <div class="param-table">
            <div class="param-table-header">
              <div class="param-col-key">参数名</div>
              <div class="param-col-value">参数值</div>
              <div class="param-col-desc">说明</div>
            </div>
            <div class="param-table-body">
              <div
                v-for="param in pathParams"
                :key="param.id"
                class="param-row"
              >
                <div class="param-col-key">
                  <span class="param-name">{{ param.name }}</span>
                  <span class="param-required" v-if="param.required">*</span>
                  <span class="param-type">({{ param.type }})</span>
                </div>
                <div class="param-col-value">
                  <el-input
                    v-model="requestData.pathParams[param.name]"
                    :placeholder="param.description || '请输入' + param.name"
                    size="small"
                  />
                </div>
                <div class="param-col-desc">{{ param.description || '-' }}</div>
              </div>
            </div>
          </div>
        </el-tab-pane>

      <!-- Body 参数 -->
      <div v-if="bodyParams.length > 0" class="param-group">
        <h4>Body 参数</h4>
        <el-radio-group v-model="bodyType" class="body-type-selector">
          <el-radio label="form">表单形式</el-radio>
          <el-radio label="json">JSON 格式</el-radio>
        </el-radio-group>
        
        <div v-if="bodyType === 'form'" class="param-list">
          <div 
            v-for="param in flatBodyParams" 
            :key="param.id"
            class="param-item"
            :style="{ paddingLeft: (param.level * 20) + 'px' }"
          >
            <div class="param-info">
              <span class="param-name">{{ param.name }}</span>
              <span class="param-type">({{ param.type }})</span>
              <span class="param-required" v-if="param.required">*</span>
            </div>
            <el-input 
              v-model="requestData.body[param.name]" 
              :placeholder="param.description || '请输入' + param.name"
              size="small"
            />
            <div class="param-desc" v-if="param.description">{{ param.description }}</div>
          </div>
        </div>
        
        <div v-else class="json-editor">
          <el-input
            v-model="requestData.bodyJson"
            type="textarea"
            :rows="10"
            placeholder="请输入 JSON 格式的请求体"
          />
        </div>
      </div>
    </div>

    <!-- 发送请求按钮 -->
    <div class="send-section">
      <el-button 
        type="primary" 
        size="large" 
        @click="sendRequest"
        :loading="requesting"
        :disabled="!selectedEnv"
      >
        {{ requesting ? '发送中...' : '发送请求' }}
      </el-button>
    </div>

    <!-- 响应结果 -->
    <div class="section" v-if="response">
      <div class="section-header">
        <h3>响应结果</h3>
        <div class="response-status">
          <el-tag 
            :type="response.success ? 'success' : 'danger'"
            size="large"
          >
            {{ response.status }} {{ response.statusText }}
          </el-tag>
          <span class="response-time">{{ response.duration }}ms</span>
        </div>
      </div>

      <el-tabs>
        <el-tab-pane label="响应数据" name="data">
          <div class="response-content">
            <div class="response-data">
              <pre class="json-viewer">{{ formatJson(response.data) }}</pre>
            </div>
            <div class="response-description" v-if="responseParams.length > 0">
              <h4>参数说明</h4>
              <div class="param-descriptions">
                <div 
                  v-for="param in flatResponseParams" 
                  :key="param.id"
                  class="param-desc-item"
                  :style="{ paddingLeft: (param.level * 20) + 'px' }"
                >
                  <span class="param-name">{{ param.name }}</span>
                  <span class="param-type">({{ param.type }})</span>
                  <span class="param-desc">{{ param.description }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="响应头" name="headers">
          <pre class="json-viewer">{{ formatJson(response.headers) }}</pre>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 错误信息 -->
    <div class="section" v-if="error">
      <div class="section-header">
        <h3>错误信息</h3>
      </div>
      <div class="error-content">
        <el-alert
          :title="error.message"
          type="error"
          :description="error.details"
          show-icon
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getEnvList } from '@/api/env'
import axios from 'axios'

export default {
  name: 'ApiDebugger',
  props: {
    apiInfo: {
      type: Object,
      required: true
    },
    headerParams: {
      type: Array,
      default: () => []
    },
    pathParams: {
      type: Array,
      default: () => []
    },
    bodyParams: {
      type: Array,
      default: () => []
    },
    responseParams: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const envList = ref([])
    const selectedEnv = ref('')
    const currentBaseUrl = ref('')
    const bodyType = ref('form')
    const requesting = ref(false)
    const response = ref(null)
    const error = ref(null)
    
    const requestData = ref({
      headers: {},
      pathParams: {},
      body: {},
      bodyJson: ''
    })

    // 扁平化参数列表
    const flatBodyParams = computed(() => {
      return flattenParams(props.bodyParams)
    })

    const flatResponseParams = computed(() => {
      return flattenParams(props.responseParams)
    })

    // 扁平化参数树
    function flattenParams(params, level = 0) {
      const result = []
      params.forEach(param => {
        result.push({ ...param, level })
        if (param.children && param.children.length > 0) {
          result.push(...flattenParams(param.children, level + 1))
        }
      })
      return result
    }

    // 获取环境列表
    async function fetchEnvList() {
      try {
        const res = await getEnvList()
        if (res.code === 200) {
          envList.value = res.data || []
          // 默认选择第一个环境
          if (envList.value.length > 0) {
            selectedEnv.value = envList.value[0].id
            onEnvChange(selectedEnv.value)
          }
        }
      } catch (err) {
        console.error('获取环境列表失败:', err)
        ElMessage.error('获取环境列表失败')
      }
    }

    // 环境变化
    function onEnvChange(envId) {
      const env = envList.value.find(e => e.id === envId)
      if (env) {
        currentBaseUrl.value = env.envUrl
      }
    }

    // 发送请求
    async function sendRequest() {
      if (!selectedEnv.value) {
        ElMessage.warning('请先选择环境')
        return
      }

      if (!props.apiInfo.path) {
        ElMessage.warning('接口路径不能为空')
        return
      }

      const startTime = Date.now()

      try {
        requesting.value = true
        error.value = null

        // 构建请求URL
        let url = currentBaseUrl.value + props.apiInfo.path

        // 替换路径参数
        Object.keys(requestData.value.pathParams).forEach(key => {
          const value = requestData.value.pathParams[key]
          if (value) {
            url = url.replace(`{${key}}`, value).replace(`:${key}`, value)
          }
        })

        // 构建请求配置
        const config = {
          method: props.apiInfo.method.toLowerCase(),
          url: url,
          headers: {
            'Content-Type': 'application/json',
            ...requestData.value.headers
          }
        }

        // 添加请求体
        if (['post', 'put', 'patch'].includes(config.method)) {
          if (bodyType.value === 'json' && requestData.value.bodyJson) {
            try {
              config.data = JSON.parse(requestData.value.bodyJson)
            } catch (e) {
              ElMessage.error('JSON 格式错误')
              return
            }
          } else if (bodyType.value === 'form') {
            config.data = requestData.value.body
          }
        }

        // 发送请求
        const res = await axios(config)
        const endTime = Date.now()

        response.value = {
          success: true,
          status: res.status,
          statusText: res.statusText,
          headers: res.headers,
          data: res.data,
          duration: endTime - startTime
        }

        ElMessage.success('请求发送成功')
      } catch (err) {
        const endTime = Date.now()

        if (err.response) {
          response.value = {
            success: false,
            status: err.response.status,
            statusText: err.response.statusText,
            headers: err.response.headers,
            data: err.response.data,
            duration: endTime - startTime
          }
        } else {
          error.value = {
            message: '请求失败',
            details: err.message
          }
        }
      } finally {
        requesting.value = false
      }
    }

    // 格式化JSON
    function formatJson(obj) {
      try {
        return JSON.stringify(obj, null, 2)
      } catch (e) {
        return obj
      }
    }

    // 监听API变化，重置请求数据
    watch(() => props.apiInfo, () => {
      requestData.value = {
        headers: {},
        pathParams: {},
        body: {},
        bodyJson: ''
      }
      response.value = null
      error.value = null
    })

    onMounted(() => {
      fetchEnvList()
    })

    return {
      envList,
      selectedEnv,
      currentBaseUrl,
      bodyType,
      requesting,
      requestData,
      response,
      error,
      flatBodyParams,
      flatResponseParams,
      onEnvChange,
      sendRequest,
      formatJson
    }
  }
}
</script>

<style scoped>
.api-debugger {
  padding: 16px;
}

.section {
  margin-bottom: 24px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.env-section {
  padding: 20px;
}

.env-selector {
  display: flex;
  align-items: center;
  gap: 16px;
}

.current-url {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-url .label {
  color: #606266;
  font-weight: 500;
}

.current-url .url {
  color: #409eff;
  font-family: monospace;
  background-color: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
}

.param-group {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.param-group:last-child {
  border-bottom: none;
}

.param-group h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.body-type-selector {
  margin-bottom: 16px;
}

.param-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.param-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.param-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-name {
  font-weight: 600;
  color: #303133;
}

.param-type {
  color: #909399;
  font-size: 12px;
}

.param-required {
  color: #f56c6c;
  font-weight: bold;
}

.param-desc {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.json-editor {
  padding: 0 20px 20px;
}

.send-section {
  text-align: center;
  margin: 24px 0;
}

.response-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.response-time {
  color: #909399;
  font-size: 14px;
}

.response-content {
  display: flex;
  gap: 20px;
  padding: 20px;
}

.response-data {
  flex: 1;
}

.response-description {
  flex: 0 0 300px;
  border-left: 1px solid #ebeef5;
  padding-left: 20px;
}

.response-description h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.param-descriptions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-desc-item {
  padding: 8px;
  background-color: #fafafa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.param-desc-item .param-name {
  font-weight: 600;
  color: #303133;
  margin-right: 8px;
}

.param-desc-item .param-type {
  color: #909399;
  font-size: 12px;
  margin-right: 8px;
}

.param-desc-item .param-desc {
  color: #606266;
  font-size: 12px;
  display: block;
  margin-top: 4px;
}

.json-viewer {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 16px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 12px;
  line-height: 1.45;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.error-content {
  padding: 20px;
}
</style>
