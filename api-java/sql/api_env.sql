/*
 Navicat Premium Dump SQL

 Source Server         : docker-mysql-8
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:13306
 Source Schema         : api

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 16/04/2025 18:20:51
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for api_env
-- ----------------------------
DROP TABLE IF EXISTS `api_env`;
CREATE TABLE `api_env`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `env_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `env_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `env_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of api_env
-- ----------------------------
INSERT INTO `api_env` VALUES (1, 'http://localhost:8080', 'dev', '开发环境');
INSERT INTO `api_env` VALUES (2, 'http://test.example.com', 'test', '测试环境');
INSERT INTO `api_env` VALUES (3, 'https://api.example.com', 'prod', '生产环境');

SET FOREIGN_KEY_CHECKS = 1;
