package me.itdong.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import me.itdong.api.entity.Roles;
import me.itdong.api.entity.Users;
import me.itdong.api.mapper.RolesDao;
import me.itdong.api.service.RolesService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 角色表(Roles)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-14 18:12:31
 */
@Service("rolesService")
public class RolesServiceImpl extends ServiceImpl<RolesDao, Roles> implements RolesService {

    /**
     * 获取角色列表
     * @return 角色列表
     */
    @Override
    public List<Roles> getRoleList() {
        return list();
    }

    /**
     * 批量为用户添加角色
     * @param userIds 用户ID列表
     * @param roleId 角色ID
     * @return 是否添加成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addUsersToRole(List<Integer> userIds, Integer roleId) {
        return userIds.stream().allMatch(userId -> 
            baseMapper.addUserRole(userId.toString(), getRoleNameById(roleId)) > 0
        );
    }

    /**
     * 创建新角色
     * @param role 角色信息
     * @return 是否创建成功
     */
    @Override
    public boolean createRole(Roles role) {
        return save(role);
    }

    /**
     * 更新角色
     * @param role 角色信息
     * @return 是否更新成功
     */
    @Override
    public boolean updateRole(Roles role) {
        // 检查角色是否存在
        Roles existingRole = getById(role.getRoleId());
        if (existingRole == null) {
            throw new RuntimeException("角色不存在");
        }
        // 更新角色信息
        return updateById(role);
    }

    /**
     * 删除角色
     * @param roleId 角色ID
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(Integer roleId) {
        return removeById(roleId);
    }

    /**
     * 从角色中移除指定用户
     * @param userIds 用户ID列表
     * @param roleId 角色ID
     * @return 是否移除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUsersFromRole(List<Integer> userIds, Integer roleId) {
        LambdaQueryWrapper<Roles> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Roles::getRoleId, roleId);
        return userIds.stream().allMatch(userId ->
            baseMapper.deleteUserRoles(userId.toString()) > 0
        );
    }

    /**
     * 删除用户的指定角色
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUserRoles(Integer userId, List<Integer> roleIds) {
        return baseMapper.deleteUserRoles(userId.toString()) > 0;
    }

    /**
     * 根据角色ID获取角色名称
     */
    private String getRoleNameById(Integer roleId) {
        Roles role = getById(roleId);
        return role != null ? role.getRoleName() : null;
    }

    /**
     * 获取角色下的用户列表
     *
     * @param roleId 角色ID
     * @return 用户列表
     */
    @Override
    public List<Users> getRoleUsers(Integer roleId) {
        List<Users> users = baseMapper.getRoleUsers(roleId);
//        return users.stream()
//                .map(user -> (Object) user)
//                .toList();
        return users;
    }
}

