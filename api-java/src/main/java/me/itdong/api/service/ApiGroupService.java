package me.itdong.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import me.itdong.api.entity.ApiGroup;

import java.util.List;

/**
 * API分组服务接口
 */
public interface ApiGroupService extends IService<ApiGroup> {
    
    /**
     * 获取API分组树
     * @return API分组树列表
     */
    List<ApiGroup> getGroupTree();
    
    /**
     * 根据ID获取分组详情（包含子分组和APIs）
     * @param id 分组ID
     * @return 分组详情
     */
    ApiGroup getGroupDetail(Integer id);
    
    /**
     * 创建API分组
     * @param apiGroup API分组信息
     * @return 是否成功
     */
    boolean createGroup(ApiGroup apiGroup);
    
    /**
     * 更新API分组
     * @param apiGroup API分组信息
     * @return 是否成功
     */
    boolean updateGroup(ApiGroup apiGroup);
    
    /**
     * 删除API分组
     * @param id 分组ID
     * @return 是否成功
     */
    boolean deleteGroup(Integer id);
} 