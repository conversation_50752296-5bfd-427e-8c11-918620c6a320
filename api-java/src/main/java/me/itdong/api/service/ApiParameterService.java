package me.itdong.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import me.itdong.api.entity.ApiParameter;
import me.itdong.api.enums.ApiParamType;

import java.util.List;

/**
 * API参数服务接口
 */
public interface ApiParameterService extends IService<ApiParameter> {
    
    /**
     * 根据API ID获取参数列表（树形结构）
     * @param apiId API ID
     * @return 参数列表
     */
    List<ApiParameter> getParameterTreeByApiId(Integer apiId);
    
    /**
     * 根据API ID和参数类型获取参数列表
     * @param apiId API ID
     * @param paramType 参数类型
     * @return 参数列表
     */
    List<ApiParameter> getParametersByApiIdAndType(Integer apiId, ApiParamType paramType);
    
    /**
     * 批量保存参数（新增、更新、删除）
     * @param apiId API ID
     * @param parameters 参数列表
     * @return 是否成功
     */
    boolean saveParameters(Integer apiId, List<ApiParameter> parameters);
    
    /**
     * 删除API的所有参数
     * @param apiId API ID
     * @return 是否成功
     */
    boolean deleteParametersByApiId(Integer apiId);
} 