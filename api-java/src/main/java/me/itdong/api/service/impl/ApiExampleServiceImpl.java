package me.itdong.api.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import me.itdong.api.entity.ApiExample;
import me.itdong.api.mapper.ApiExampleMapper;
import me.itdong.api.service.ApiExampleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * API示例服务实现
 */
@Service
public class ApiExampleServiceImpl extends ServiceImpl<ApiExampleMapper, ApiExample> implements ApiExampleService {

    @Override
    public List<ApiExample> getExamplesByApiId(Integer apiId, String type) {
        LambdaQueryWrapper<ApiExample> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApiExample::getApiId, apiId);
        
        // 如果指定了类型，则按类型筛选
        if (type != null && !type.isEmpty()) {
            queryWrapper.eq(ApiExample::getType, type);
        }
        
        queryWrapper.orderByDesc(ApiExample::getIsDefault);
        queryWrapper.orderByDesc(ApiExample::getUpdateTime);
        
        return this.list(queryWrapper);
    }

    @Override
    public ApiExample getDefaultExample(Integer apiId, String type) {
        LambdaQueryWrapper<ApiExample> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApiExample::getApiId, apiId)
                .eq(ApiExample::getType, type)
                .eq(ApiExample::getIsDefault, 1);
        
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveExample(ApiExample example) {
        // 设置默认值
        if (example.getIsDefault() == null) {
            example.setIsDefault(0);
        }
        
        // 如果是默认示例，则将同类型的其他示例设为非默认
        if (example.getIsDefault() == 1) {
            this.resetDefaultExample(example.getApiId(), example.getType());
        }
        
        // 设置创建/更新信息
        Integer currentUserId = StpUtil.getLoginIdAsInt();
        LocalDateTime now = LocalDateTime.now();
        
        example.setCreateBy(currentUserId);
        example.setUpdateBy(currentUserId);
        example.setCreateTime(now);
        example.setUpdateTime(now);
        example.setIsDeleted(0); // 设置为未删除状态
        
        return this.save(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExample(ApiExample example) {
        // 如果是默认示例，则将同类型的其他示例设为非默认
        if (example.getIsDefault() != null && example.getIsDefault() == 1) {
            // 获取当前示例信息
            ApiExample currentExample = this.getById(example.getId());
            if (currentExample != null) {
                this.resetDefaultExample(currentExample.getApiId(), currentExample.getType());
            }
        }
        
        // 设置更新信息
        Integer currentUserId = StpUtil.getLoginIdAsInt();
        example.setUpdateBy(currentUserId);
        example.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDefaultExample(Integer id) {
        // 获取当前示例信息
        ApiExample example = this.getById(id);
        if (example == null) {
            return false;
        }
        
        // 将同类型的其他示例设为非默认
        this.resetDefaultExample(example.getApiId(), example.getType());
        
        // 设置当前示例为默认
        example.setIsDefault(1);
        example.setUpdateBy(StpUtil.getLoginIdAsInt());
        example.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteExamplesByApiId(Integer apiId) {
        // 使用逻辑删除，将指定API ID的所有示例标记为已删除
        LambdaUpdateWrapper<ApiExample> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ApiExample::getApiId, apiId)
                     .set(ApiExample::getIsDeleted, 1);
        return this.update(updateWrapper);
        
        // 使用MyBatis-Plus的逻辑删除功能时，可以简化为：
        // return this.remove(new LambdaQueryWrapper<ApiExample>().eq(ApiExample::getApiId, apiId));
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveExamples(Integer apiId, List<ApiExample> examples) {
        // 获取当前API的所有示例
        List<ApiExample> existingExamples = this.getExamplesByApiId(apiId, null);
        
        // 收集现有示例ID
        List<Integer> existingIds = existingExamples.stream()
                .map(ApiExample::getId)
                .filter(id -> id != null)
                .collect(java.util.stream.Collectors.toList());
        
        // 收集新示例ID
        List<Integer> newIds = examples.stream()
                .map(ApiExample::getId)
                .filter(id -> id != null)
                .collect(java.util.stream.Collectors.toList());
        
        // 需要删除的示例ID
        List<Integer> toDeleteIds = existingIds.stream()
                .filter(id -> !newIds.contains(id))
                .collect(java.util.stream.Collectors.toList());
        
        // 删除不再需要的示例
        if (!toDeleteIds.isEmpty()) {
            this.removeByIds(toDeleteIds);
        }
        
        // 按类型分组，用于处理默认示例
        Map<String, List<ApiExample>> examplesByType = examples.stream()
                .collect(java.util.stream.Collectors.groupingBy(ApiExample::getType));
        
        // 遍历每种类型，确保每种类型只有一个默认示例
        for (Map.Entry<String, List<ApiExample>> entry : examplesByType.entrySet()) {
            String type = entry.getKey();
            List<ApiExample> examplesOfType = entry.getValue();
            
            // 找出设置为默认的示例
            List<ApiExample> defaultExamples = examplesOfType.stream()
                    .filter(e -> e.getIsDefault() != null && e.getIsDefault() == 1)
                    .collect(java.util.stream.Collectors.toList());
            
            // 如果有多个默认示例，只保留第一个
            if (defaultExamples.size() > 1) {
                ApiExample firstDefault = defaultExamples.get(0);
                for (int i = 1; i < defaultExamples.size(); i++) {
                    defaultExamples.get(i).setIsDefault(0);
                }
            }
            
            // 如果没有默认示例且该类型有示例，将第一个设为默认
            if (defaultExamples.isEmpty() && !examplesOfType.isEmpty()) {
                examplesOfType.get(0).setIsDefault(1);
            }
        }
        
        // 设置创建/更新信息
        Integer currentUserId = StpUtil.getLoginIdAsInt();
        LocalDateTime now = LocalDateTime.now();
        
        // 保存或更新示例
        for (ApiExample example : examples) {
            example.setApiId(apiId);
            example.setIsDeleted(0); // 设置为未删除状态
            
            if (example.getId() == null) {
                // 新示例，设置创建信息
                example.setCreateBy(currentUserId);
                example.setUpdateBy(currentUserId);
                example.setCreateTime(now);
                example.setUpdateTime(now);
                this.save(example);
            } else {
                // 更新示例
                example.setUpdateBy(currentUserId);
                example.setUpdateTime(now);
                this.updateById(example);
            }
        }
        
        return true;
    }
    
    /**
     * 重置同类型的默认示例
     * @param apiId API ID
     * @param type 示例类型
     */
    private void resetDefaultExample(Integer apiId, String type) {
        LambdaQueryWrapper<ApiExample> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApiExample::getApiId, apiId)
                .eq(ApiExample::getType, type)
                .eq(ApiExample::getIsDefault, 1);
        
        ApiExample defaultExample = this.getOne(queryWrapper);
        if (defaultExample != null) {
            defaultExample.setIsDefault(0);
            defaultExample.setUpdateBy(StpUtil.getLoginIdAsInt());
            defaultExample.setUpdateTime(LocalDateTime.now());
            this.updateById(defaultExample);
        }
    }
} 