package me.itdong.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import me.itdong.api.entity.ApiExample;

import java.util.List;

/**
 * API示例服务接口
 */
public interface ApiExampleService extends IService<ApiExample> {
    
    /**
     * 根据API ID获取示例列表
     * @param apiId API ID
     * @param type 示例类型(request/response)，可选
     * @return 示例列表
     */
    List<ApiExample> getExamplesByApiId(Integer apiId, String type);
    
    /**
     * 获取默认示例
     * @param apiId API ID
     * @param type 示例类型(request/response)
     * @return 默认示例
     */
    ApiExample getDefaultExample(Integer apiId, String type);
    
    /**
     * 保存示例
     * @param example 示例信息
     * @return 是否成功
     */
    boolean saveExample(ApiExample example);
    
    /**
     * 更新示例
     * @param example 示例信息
     * @return 是否成功
     */
    boolean updateExample(ApiExample example);
    
    /**
     * 设置默认示例
     * @param id 示例ID
     * @return 是否成功
     */
    boolean setDefaultExample(Integer id);
    
    /**
     * 删除API的所有示例
     * @param apiId API ID
     * @return 是否成功
     */
    boolean deleteExamplesByApiId(Integer apiId);
    
    /**
     * 批量保存示例（新增、更新、删除）
     * @param apiId API ID
     * @param examples 示例列表
     * @return 是否成功
     */
    boolean batchSaveExamples(Integer apiId, List<ApiExample> examples);
} 