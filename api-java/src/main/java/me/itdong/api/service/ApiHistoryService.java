package me.itdong.api.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import me.itdong.api.entity.ApiHistory;
import me.itdong.api.enums.ApiDevStatus;
import me.itdong.api.vo.ApiHistoryVO;

import java.util.List;

/**
 * API更新历史记录服务接口
 */
public interface ApiHistoryService extends IService<ApiHistory> {

    /**
     * 添加API状态变更记录
     *
     * @param apiId       API ID
     * @param oldStatus   旧状态
     * @param newStatus   新状态
     * @param operatorId  操作人ID
     * @param remarks     备注
     * @return 是否成功
     */
    boolean addStatusChangeRecord(Integer apiId, ApiDevStatus oldStatus, ApiDevStatus newStatus, Long operatorId, String remarks);

    /**
     * 分页查询API历史记录
     *
     * @param page    分页参数
     * @param apiId   API ID
     * @return 分页结果
     */
    Page<ApiHistoryVO> pageApiHistory(Page<ApiHistory> page, Integer apiId);

    /**
     * 获取API的历史记录列表
     *
     * @param apiId   API ID
     * @return 历史记录列表
     */
    List<ApiHistoryVO> getApiHistoryList(Integer apiId);
} 