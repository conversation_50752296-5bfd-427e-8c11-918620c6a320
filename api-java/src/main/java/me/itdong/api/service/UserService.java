package me.itdong.api.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import me.itdong.api.entity.Users;
import me.itdong.api.entity.vo.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * (User)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-12 21:36:44
 */
public interface UserService extends IService<Users> {

    Integer login(UserLoginVo user);
    UserInfoVo getUserInfo(int userId);

    /**
     * 创建新用户
     * @param userCreate 用户创建信息（包含用户名、密码、真实姓名和角色ID列表）
     * @return 是否创建成功
     */
    boolean createUser(UserCreateVO userCreate);

    /**
     * 更新用户信息
     * @param user 用户信息
     * @return 是否更新成功
     */
    boolean updateUserInfo(UserListVo user);

    /**
     * 通过ID获取用户名
     * @param userId 用户ID
     * @return 用户名
     */
    String getUserNameById(Integer userId);

    /**
     * 批量获取用户名
     * @param userIds 用户ID列表
     * @return 用户ID到用户名的映射
     */
    Map<Long, String> getUserNameBatch(List<Long> userIds);

    /**
     * 获取用户列表
     * @return 用户列表
     */
    List<UserListVo> getUserList();
}

