package me.itdong.api.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import me.itdong.api.entity.Roles;
import me.itdong.api.entity.SysLog;
import me.itdong.api.entity.Users;
import me.itdong.api.entity.vo.*;
import me.itdong.api.enums.BusinessExceptionEnum;
import me.itdong.api.exception.BusinessException;
import me.itdong.api.mapper.PermissionsMapper;
import me.itdong.api.mapper.RolesDao;
import me.itdong.api.mapper.SysLogMapper;
import me.itdong.api.mapper.UserMapper;
import me.itdong.api.service.FileUploadService;
import me.itdong.api.service.RolesService;
import me.itdong.api.service.UserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * (User)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-12 21:36:45
 */
@Slf4j
@Service("userService")
public class UserServiceImpl extends ServiceImpl<UserMapper, Users> implements UserService {
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RolesDao rolesMapper;
    @Autowired
    private PermissionsMapper permissionsMapper;
    @Autowired
    private RolesService rolesService;

    /**
     * 生成强密码
     * 包含大小写字母、数字和特殊字符
     *
     * @return 生成的强密码
     */
    private String generateStrongPassword() {
        String upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String lowerCase = "abcdefghijklmnopqrstuvwxyz";
        String numbers = "0123456789";
        String specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";

        StringBuilder password = new StringBuilder();
        // 确保至少包含一个大写字母
        password.append(upperCase.charAt((int) (Math.random() * upperCase.length())));
        // 确保至少包含一个小写字母
        password.append(lowerCase.charAt((int) (Math.random() * lowerCase.length())));
        // 确保至少包含一个数字
        password.append(numbers.charAt((int) (Math.random() * numbers.length())));
        // 确保至少包含一个特殊字符
        password.append(specialChars.charAt((int) (Math.random() * specialChars.length())));

        // 添加额外的随机字符，直到达到12位
        String allChars = upperCase + lowerCase + numbers + specialChars;
        while (password.length() < 12) {
            password.append(allChars.charAt((int) (Math.random() * allChars.length())));
        }

        // 打乱密码字符顺序
        String shuffled = password.toString();
        char[] shuffledChars = shuffled.toCharArray();
        for (int i = shuffledChars.length - 1; i > 0; i--) {
            int j = (int) (Math.random() * (i + 1));
            char temp = shuffledChars[i];
            shuffledChars[i] = shuffledChars[j];
            shuffledChars[j] = temp;
        }

        return new String(shuffledChars);
    }

    @Override
    public Integer login(UserLoginVo user) {
        LambdaQueryWrapper<Users> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Users::getUsername, user.getUsername());
        Users loginUser = userMapper.selectOne(lambdaQueryWrapper);

        if (loginUser != null) {
            if (BCrypt.checkpw(user.getPassword().trim(), loginUser.getPassword())) {
                // 验证用户状态
                return loginUser.getUserId();

            } else {
                throw new BusinessException(BusinessExceptionEnum.PASSWORD_ERROR);
            }
        }
        throw new BusinessException(BusinessExceptionEnum.ROLE_NOT_FOUND);
    }

    /**
     * 创建新用户
     *
     * @param userCreate 用户创建VO对象
     * @return 是否创建成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createUser(UserCreateVO userCreate) {
        // 检查用户名是否已存在
        LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Users::getUsername, userCreate.getUsername());
        if (userMapper.selectCount(queryWrapper) > 0) {
            throw new BusinessException(BusinessExceptionEnum.USER_ALREADY_EXISTS);
        }

        // 创建用户对象
        Users user = new Users();
        user.setUsername(userCreate.getUsername());
        user.setName(userCreate.getName());

        // 密码加密
        user.setPassword(BCrypt.hashpw(userCreate.getPassword(), BCrypt.gensalt(6)));

        // 保存用户
        boolean success = userMapper.insert(user) > 0;

        // 分配角色
        if (success && userCreate.getRoleIds() != null && !userCreate.getRoleIds().isEmpty()) {
            log.info("开始分配角色，用户ID：{}，角色IDs：{}", user.getUserId(), userCreate.getRoleIds());
            for (Integer roleId : userCreate.getRoleIds()) {
                // 验证角色是否存在
                Roles role = rolesMapper.selectById(roleId);
                if (role == null) {
                    log.error("角色不存在，角色ID：{}", roleId);
                    throw new BusinessException(BusinessExceptionEnum.ROLE_NOT_FOUND);
                }

                log.info("添加用户角色关联，用户ID：{}，角色ID：{}", user.getUserId(), roleId);
                // 添加用户角色关联
                int result = rolesMapper.addUserRole(String.valueOf(user.getUserId()), String.valueOf(roleId));
                log.info("添加用户角色关联结果：{}", result);
            }
        }

        return success;
    }

    @Override
    public UserInfoVo getUserInfo(int loginId) {
        Users users = userMapper.selectById(loginId);
        UserInfoVo userInfoVo = new UserInfoVo();
        BeanUtils.copyProperties(users, userInfoVo);
        userInfoVo.setRoles(rolesMapper.getall(String.valueOf(loginId)));
        return userInfoVo;
    }

    @Override
    public boolean updateUserInfo(UserListVo userVo) {
        Users user = new Users();
        BeanUtils.copyProperties(userVo, user);

        // 如果有密码，进行加密
        if (user.getPassword() != null) {
            user.setPassword(BCrypt.hashpw(user.getPassword(), BCrypt.gensalt(6)));
            StpUtil.logout(user.getUserId());
        }

        // 更新用户基本信息
        boolean success = userMapper.updateById(user) > 0;

        // 如果有角色信息，更新用户角色
        if (success && userVo.getRoles() != null && !userVo.getRoles().isEmpty()) {
            log.info("更新用户角色，用户ID：{}，角色：{}", user.getUserId(), userVo.getRoles());
            // 先删除用户现有角色
            int deleteResult = rolesMapper.deleteUserRoles(String.valueOf(user.getUserId()));
            log.info("删除原有角色结果：{}", deleteResult);

            // 添加新角色
            for (String roleName : userVo.getRoles()) {
                // 先根据角色名称获取角色ID
                LambdaQueryWrapper<Roles> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Roles::getRoleName, roleName);
                Roles role = rolesMapper.selectOne(queryWrapper);

                if (role != null) {
                    log.info("为用户添加角色，用户ID：{}，角色ID：{}", user.getUserId(), role.getRoleId());
                    int result = rolesMapper.addUserRole(String.valueOf(user.getUserId()), String.valueOf(role.getRoleId()));
                    log.info("添加角色结果：{}", result);
                } else {
                    log.warn("角色不存在：{}", roleName);
                }
            }
        }

        return success;
    }

    @Override
    public String getUserNameById(Integer userId) {
        if (userId == null) {
            return "未知用户";
        }

        Users user = userMapper.selectById(userId);
        return user != null ? (user.getName() != null ? user.getName() : user.getUsername()) : "未知用户";
    }

    @Override
    public Map<Long, String> getUserNameBatch(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Map.of();
        }

        // 批量查询用户信息
        List<Users> users = userMapper.selectBatchIds(userIds);

        // 构建用户ID到用户名的映射
        return users.stream().collect(Collectors.toMap(
                user -> Long.valueOf(user.getUserId()),
                user -> user.getName() != null ? user.getName() : user.getUsername(),
                (v1, v2) -> v1  // 如果有重复的键，保留第一个值
        ));
    }

    /**
     * 实现getUserList方法，返回所有用户列表及其角色信息
     *
     * @return 用户列表
     */
    @Override
    public List<UserListVo> getUserList() {
        // 获取所有用户
        List<Users> users = list();

        // 转换为UserListVo并填充角色信息
        return users.stream().map(user -> {
            UserListVo userListVo = new UserListVo();
            userListVo.setUserId(user.getUserId());
            userListVo.setUserName(user.getUsername());
            userListVo.setRealName(user.getName());
            userListVo.setAvatar(user.getAvatar() != null ? user.getAvatar() : "");
            userListVo.setCreatedTime(user.getCreatedTime());
            userListVo.setUpdatedTime(user.getUpdatedTime());

            // 获取用户角色列表
            List<String> roleNames = rolesMapper.getall(String.valueOf(user.getUserId()));
            if (roleNames == null) {
                roleNames = new ArrayList<>();
            }
            userListVo.setRoles(roleNames);

            // 初始化权限列表，避免前端显示时出现空指针异常
            userListVo.setPermissions(new ArrayList<>());

            return userListVo;
        }).collect(Collectors.toList());
    }
}

