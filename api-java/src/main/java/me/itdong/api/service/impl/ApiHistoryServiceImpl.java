package me.itdong.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import me.itdong.api.entity.ApiHistory;
import me.itdong.api.entity.ApiInfo;
import me.itdong.api.enums.ApiDevStatus;
import me.itdong.api.mapper.ApiHistoryMapper;
import me.itdong.api.service.ApiHistoryService;
import me.itdong.api.service.ApiInfoService;
import me.itdong.api.service.UserService;
import me.itdong.api.vo.ApiHistoryVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * API更新历史记录服务实现类
 */
@Service
public class ApiHistoryServiceImpl extends ServiceImpl<ApiHistoryMapper, ApiHistory> implements ApiHistoryService {

    @Autowired
    private ApiInfoService apiInfoService;

    @Autowired
    private UserService userService;

    @Override
    public boolean addStatusChangeRecord(Integer apiId, ApiDevStatus oldStatus, ApiDevStatus newStatus, Long operatorId, String remarks) {
        ApiHistory history = new ApiHistory()
                .setApiId(apiId)
                .setOldStatus(oldStatus != null ? oldStatus.name() : null)
                .setNewStatus(newStatus.name())
                .setOperatorId(operatorId)
                .setOperationTime(LocalDateTime.now())
                .setRemarks(remarks);
        
        return this.save(history);
    }

    @Override
    public Page<ApiHistoryVO> pageApiHistory(Page<ApiHistory> page, Integer apiId) {
        // 构建查询条件
        LambdaQueryWrapper<ApiHistory> queryWrapper = new LambdaQueryWrapper<>();
        if (apiId != null) {
            queryWrapper.eq(ApiHistory::getApiId, apiId);
        }
        queryWrapper.orderByDesc(ApiHistory::getOperationTime);
        
        // 执行分页查询
        Page<ApiHistory> historyPage = this.page(page, queryWrapper);
        
        // 转换为VO对象
        Page<ApiHistoryVO> resultPage = new Page<>();
        BeanUtils.copyProperties(historyPage, resultPage, "records");
        
        if (historyPage.getRecords() != null && !historyPage.getRecords().isEmpty()) {
            // 提取API ID列表和操作人ID列表
            List<Integer> apiIds = historyPage.getRecords().stream()
                    .map(ApiHistory::getApiId)
                    .distinct()
                    .collect(Collectors.toList());
            
            List<Long> operatorIds = historyPage.getRecords().stream()
                    .map(ApiHistory::getOperatorId)
                    .filter(id -> id != null)
                    .distinct()
                    .collect(Collectors.toList());
            
            // 批量查询API信息
            Map<Integer, String> apiNameMap = apiInfoService.listByIds(apiIds).stream()
                    .collect(Collectors.toMap(
                        ApiInfo::getId, 
                        ApiInfo::getName,
                        (v1, v2) -> v1 // 如果有重复键，保留第一个值
                    ));
            
            // 批量查询用户信息
            Map<Long, String> userNameMap = operatorIds.isEmpty() ? 
                    Map.of() : userService.getUserNameBatch(operatorIds);
            
            // 转换记录
            List<ApiHistoryVO> voList = historyPage.getRecords().stream().map(history -> {
                ApiHistoryVO vo = new ApiHistoryVO();
                BeanUtils.copyProperties(history, vo);
                
                // 设置API名称
                vo.setApiName(apiNameMap.getOrDefault(history.getApiId(), "未知API"));
                
                // 设置操作人姓名
                if (history.getOperatorId() != null) {
                    vo.setOperatorName(userNameMap.getOrDefault(history.getOperatorId(), "未知用户"));
                }
                
                // 设置状态名称
                if (history.getOldStatus() != null) {
                    try {
                        ApiDevStatus oldStatus = ApiDevStatus.valueOf(history.getOldStatus());
                        vo.setOldStatusName(oldStatus.getDisplayName());
                    } catch (IllegalArgumentException e) {
                        vo.setOldStatusName(history.getOldStatus());
                    }
                }
                
                if (history.getNewStatus() != null) {
                    try {
                        ApiDevStatus newStatus = ApiDevStatus.valueOf(history.getNewStatus());
                        vo.setNewStatusName(newStatus.getDisplayName());
                    } catch (IllegalArgumentException e) {
                        vo.setNewStatusName(history.getNewStatus());
                    }
                }
                
                return vo;
            }).collect(Collectors.toList());
            
            resultPage.setRecords(voList);
        } else {
            resultPage.setRecords(new ArrayList<>());
        }
        
        return resultPage;
    }

    @Override
    public List<ApiHistoryVO> getApiHistoryList(Integer apiId) {
        if (apiId == null) {
            return new ArrayList<>();
        }
        
        // 构建查询条件
        LambdaQueryWrapper<ApiHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApiHistory::getApiId, apiId);
        queryWrapper.orderByDesc(ApiHistory::getOperationTime);
        
        // 执行查询
        List<ApiHistory> historyList = this.list(queryWrapper);
        
        if (historyList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 提取操作人ID列表
        List<Long> operatorIds = historyList.stream()
                .map(ApiHistory::getOperatorId)
                .filter(id -> id != null)
                .distinct()
                .collect(Collectors.toList());
        
        // 批量查询API信息
        ApiInfo apiInfo = apiInfoService.getById(apiId);
        
        // 批量查询用户信息
        Map<Long, String> userNameMap = operatorIds.isEmpty() ? 
                Map.of() : userService.getUserNameBatch(operatorIds);
        
        // 转换记录
        return historyList.stream().map(history -> {
            ApiHistoryVO vo = new ApiHistoryVO();
            BeanUtils.copyProperties(history, vo);
            
            // 设置API名称
            vo.setApiName(apiInfo != null ? apiInfo.getName() : "未知API");
            
            // 设置操作人姓名
            if (history.getOperatorId() != null) {
                vo.setOperatorName(userNameMap.getOrDefault(history.getOperatorId(), "未知用户"));
            }
            
            // 设置状态名称
            if (history.getOldStatus() != null) {
                try {
                    ApiDevStatus oldStatus = ApiDevStatus.valueOf(history.getOldStatus());
                    vo.setOldStatusName(oldStatus.getDisplayName());
                } catch (IllegalArgumentException e) {
                    vo.setOldStatusName(history.getOldStatus());
                }
            }
            
            if (history.getNewStatus() != null) {
                try {
                    ApiDevStatus newStatus = ApiDevStatus.valueOf(history.getNewStatus());
                    vo.setNewStatusName(newStatus.getDisplayName());
                } catch (IllegalArgumentException e) {
                    vo.setNewStatusName(history.getNewStatus());
                }
            }
            
            return vo;
        }).collect(Collectors.toList());
    }
} 