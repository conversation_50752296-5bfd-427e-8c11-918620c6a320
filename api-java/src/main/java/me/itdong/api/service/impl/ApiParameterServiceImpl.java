package me.itdong.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import me.itdong.api.entity.ApiParameter;
import me.itdong.api.enums.ApiParamType;
import me.itdong.api.mapper.ApiParameterMapper;
import me.itdong.api.service.ApiParameterService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * API参数服务实现类
 */
@Service
public class ApiParameterServiceImpl extends ServiceImpl<ApiParameterMapper, ApiParameter> implements ApiParameterService {

    @Override
    public List<ApiParameter> getParameterTreeByApiId(Integer apiId) {
        // 获取API所有参数
        List<ApiParameter> allParameters = this.list(new LambdaQueryWrapper<ApiParameter>()
                .eq(ApiParameter::getApiId, apiId)
                .orderByAsc(ApiParameter::getSortOrder));
        
        if (allParameters.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 构建参数树
        return buildParameterTree(allParameters);
    }

    @Override
    public List<ApiParameter> getParametersByApiIdAndType(Integer apiId, ApiParamType paramType) {
        return this.list(new LambdaQueryWrapper<ApiParameter>()
                .eq(ApiParameter::getApiId, apiId)
                .eq(ApiParameter::getParamType, paramType.name())
                .orderByAsc(ApiParameter::getSortOrder));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveParameters(Integer apiId, List<ApiParameter> parameters) {
        // 获取现有参数
        List<ApiParameter> existingParameters = this.list(new LambdaQueryWrapper<ApiParameter>()
                .eq(ApiParameter::getApiId, apiId));
        
        // 收集现有参数ID
        List<Integer> existingIds = existingParameters.stream()
                .map(ApiParameter::getId)
                .collect(Collectors.toList());
        
        // 收集新参数ID
        List<Integer> newIds = parameters.stream()
                .filter(p -> p.getId() != null)
                .map(ApiParameter::getId)
                .collect(Collectors.toList());
        
        // 需要删除的参数ID
        List<Integer> toDeleteIds = existingIds.stream()
                .filter(id -> !newIds.contains(id))
                .collect(Collectors.toList());
        
        // 删除不再需要的参数
        if (!toDeleteIds.isEmpty()) {
            this.removeByIds(toDeleteIds);
        }
        
        // 保存/更新参数
        for (ApiParameter parameter : parameters) {
            parameter.setApiId(apiId);
            
            // 如果没有排序，设置默认排序
            if (parameter.getSortOrder() == null) {
                parameter.setSortOrder(0);
            }
            
            // 设置为未删除状态
            parameter.setIsDeleted(0);
            
            if (parameter.getId() == null) {
                // 新参数，插入
                this.save(parameter);
            } else {
                // 现有参数，更新
                this.updateById(parameter);
            }
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteParametersByApiId(Integer apiId) {
        // 使用逻辑删除，将指定API ID的所有参数标记为已删除
        LambdaUpdateWrapper<ApiParameter> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ApiParameter::getApiId, apiId)
                     .set(ApiParameter::getIsDeleted, 1);
        return this.update(updateWrapper);
        
        // 使用MyBatis-Plus的逻辑删除功能时，可以简化为：
        // return this.remove(new LambdaQueryWrapper<ApiParameter>().eq(ApiParameter::getApiId, apiId));
    }
    
    /**
     * 构建参数树结构
     * @param parameters 所有参数列表
     * @return 树形结构的参数列表
     */
    private List<ApiParameter> buildParameterTree(List<ApiParameter> parameters) {
        // 根据父ID分组
        Map<Integer, List<ApiParameter>> parentMap = new HashMap<>();
        
        // 分组处理，parentId为null的作为根节点
        List<ApiParameter> rootParameters = new ArrayList<>();
        
        for (ApiParameter parameter : parameters) {
            if (parameter.getParentId() == null) {
                rootParameters.add(parameter);
            } else {
                List<ApiParameter> children = parentMap.getOrDefault(parameter.getParentId(), new ArrayList<>());
                children.add(parameter);
                parentMap.put(parameter.getParentId(), children);
            }
        }
        
        // 递归设置子参数
        for (ApiParameter param : parameters) {
            List<ApiParameter> children = parentMap.get(param.getId());
            if (children != null) {
                param.setChildren(children);
            }
        }
        
        return rootParameters;
    }
} 