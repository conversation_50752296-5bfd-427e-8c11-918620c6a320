package me.itdong.api.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import me.itdong.api.entity.ApiGroup;
import me.itdong.api.entity.ApiInfo;
import me.itdong.api.mapper.ApiGroupMapper;
import me.itdong.api.mapper.ApiInfoMapper;
import me.itdong.api.service.ApiGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * API分组服务实现
 */
@Service
public class ApiGroupServiceImpl extends ServiceImpl<ApiGroupMapper, ApiGroup> implements ApiGroupService {

    @Autowired
    private ApiInfoMapper apiInfoMapper;

    @Override
    public List<ApiGroup> getGroupTree() {
        // 查询所有分组
        List<ApiGroup> allGroups = this.list();
        
        // 按照parentId分组
        Map<Integer, List<ApiGroup>> groupMap = allGroups.stream()
                .collect(Collectors.groupingBy(group -> group.getParentId() == null ? 0 : group.getParentId()));
        
        // 构建树形结构
        List<ApiGroup> rootGroups = groupMap.getOrDefault(0, new ArrayList<>());
        rootGroups.forEach(group -> buildGroupTree(group, groupMap));
        
        // 查询并设置所有分组的APIs
        rootGroups.forEach(group -> fillGroupApis(group));
        
        return rootGroups;
    }

    @Override
    public ApiGroup getGroupDetail(Integer id) {
        // 获取分组信息
        ApiGroup group = this.getById(id);
        if (group == null) {
            return null;
        }
        
        // 获取子分组
        List<ApiGroup> children = this.list(new LambdaQueryWrapper<ApiGroup>()
                .eq(ApiGroup::getParentId, id)
                .orderByAsc(ApiGroup::getSortOrder));
        group.setChildren(children);
        
        // 获取分组下的API列表
        List<ApiInfo> apis = apiInfoMapper.selectList(new LambdaQueryWrapper<ApiInfo>()
                .eq(ApiInfo::getGroupId, id));
        group.setApis(apis);
        
        return group;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createGroup(ApiGroup apiGroup) {
        // 设置创建信息
        Integer currentUserId = StpUtil.getLoginIdAsInt();
        LocalDateTime now = LocalDateTime.now();
        
        apiGroup.setCreateBy(currentUserId);
        apiGroup.setUpdateBy(currentUserId);
        apiGroup.setCreateTime(now);
        apiGroup.setUpdateTime(now);
        
        // 如果没有设置排序，默认排序为0
        if (apiGroup.getSortOrder() == null) {
            apiGroup.setSortOrder(0);
        }
        
        return this.save(apiGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateGroup(ApiGroup apiGroup) {
        // 设置更新信息
        Integer currentUserId = StpUtil.getLoginIdAsInt();
        apiGroup.setUpdateBy(currentUserId);
        apiGroup.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(apiGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteGroup(Integer id) {
        // 获取分组信息
        ApiGroup group = this.getById(id);
        if (group == null) {
            return false;
        }
        
        // 检查是否有子分组
        long childCount = this.count(new LambdaQueryWrapper<ApiGroup>()
                .eq(ApiGroup::getParentId, id));
        if (childCount > 0) {
            throw new RuntimeException("该分组下有子分组，不能删除");
        }
        
        // 检查是否有API
        long apiCount = apiInfoMapper.selectCount(new LambdaQueryWrapper<ApiInfo>()
                .eq(ApiInfo::getGroupId, id));
        if (apiCount > 0) {
            throw new RuntimeException("该分组下有API，不能删除");
        }
        
        // 删除分组
        return this.removeById(id);
    }
    
    /**
     * 递归构建分组树
     * @param parent 父分组
     * @param groupMap 分组Map
     */
    private void buildGroupTree(ApiGroup parent, Map<Integer, List<ApiGroup>> groupMap) {
        List<ApiGroup> children = groupMap.getOrDefault(parent.getId(), new ArrayList<>());
        if (!children.isEmpty()) {
            parent.setChildren(children);
            children.forEach(child -> buildGroupTree(child, groupMap));
        }
    }
    
    /**
     * 递归填充分组的APIs数据
     * @param group 分组
     */
    private void fillGroupApis(ApiGroup group) {
        // 查询当前分组下的APIs
        List<ApiInfo> apis = apiInfoMapper.selectList(new LambdaQueryWrapper<ApiInfo>()
                .eq(ApiInfo::getGroupId, group.getId()));
        group.setApis(apis);
        
        // 递归处理子分组
        if (group.getChildren() != null && !group.getChildren().isEmpty()) {
            group.getChildren().forEach(this::fillGroupApis);
        }
    }
} 