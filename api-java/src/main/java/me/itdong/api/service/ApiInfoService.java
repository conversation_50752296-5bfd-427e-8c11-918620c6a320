package me.itdong.api.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import me.itdong.api.entity.ApiInfo;
import me.itdong.api.enums.ApiDevStatus;
import me.itdong.api.vo.ApiGroupTreeVO;

import java.util.List;

/**
 * API信息服务接口
 */
public interface ApiInfoService extends IService<ApiInfo> {
    
    /**
     * 分页查询API信息
     * @param page 页码
     * @param size 每页大小
     * @param name 接口名称（可选）
     * @param method 请求方法（可选）
     * @param path 接口路径（可选）
     * @param groupId 分组ID（可选）
     * @param devStatus 开发状态（可选）
     * @return 分页结果
     */
    IPage<ApiInfo> pageApiInfo(Integer page, Integer size, String name, String method, 
                             String path, Integer groupId, ApiDevStatus devStatus);
    
    /**
     * 根据分组ID获取API列表
     * @param groupId 分组ID
     * @return API列表
     */
    List<ApiInfo> getApisByGroupId(Integer groupId);
    
    /**
     * 获取API详情（包含参数信息）
     * @param id API ID
     * @return API详情
     */
    ApiInfo getApiDetail(Integer id);
    
    /**
     * 创建API
     * @param apiInfo API信息
     * @return 是否成功
     */
    boolean createApi(ApiInfo apiInfo);
    
    /**
     * 更新API
     * @param apiInfo API信息
     * @return 是否成功
     */
    boolean updateApi(ApiInfo apiInfo);
    
    /**
     * 删除API
     * @param id API ID
     * @return 是否成功
     */
    boolean deleteApi(Integer id);
    
    /**
     * 更新API开发状态
     * @param id API ID
     * @param devStatus 开发状态
     * @return 是否成功
     */
    boolean updateApiDevStatus(Integer id, ApiDevStatus devStatus);
    
    /**
     * 获取带API的分组树，支持按开发状态筛选
     * @param devStatus 开发状态（可选）
     * @return 分组树
     */
    List<ApiGroupTreeVO> getGroupTreeWithApis(String devStatus);
} 