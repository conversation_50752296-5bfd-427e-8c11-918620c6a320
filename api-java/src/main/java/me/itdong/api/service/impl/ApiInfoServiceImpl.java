package me.itdong.api.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import me.itdong.api.entity.ApiGroup;
import me.itdong.api.entity.ApiInfo;
import me.itdong.api.entity.ApiParameter;
import me.itdong.api.enums.ApiDevStatus;
import me.itdong.api.mapper.ApiGroupMapper;
import me.itdong.api.mapper.ApiInfoMapper;
import me.itdong.api.mapper.ApiParameterMapper;
import me.itdong.api.service.ApiInfoService;
import me.itdong.api.service.ApiParameterService;
import me.itdong.api.vo.ApiGroupTreeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * API信息服务实现类
 */
@Service
public class ApiInfoServiceImpl extends ServiceImpl<ApiInfoMapper, ApiInfo> implements ApiInfoService {

    @Autowired
    private ApiInfoMapper apiInfoMapper;
    
    @Autowired
    private ApiParameterMapper apiParameterMapper;
    
    @Autowired
    private ApiGroupMapper apiGroupMapper;
    
    @Autowired
    private ApiParameterService apiParameterService;

    @Override
    public IPage<ApiInfo> pageApiInfo(Integer page, Integer size, String name, String method, String path, Integer groupId, ApiDevStatus devStatus) {
        Page<ApiInfo> pageParam = new Page<>(page, size);
        
        LambdaQueryWrapper<ApiInfo> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.hasText(name)) {
            queryWrapper.like(ApiInfo::getName, name);
        }
        
        if (StringUtils.hasText(method)) {
            queryWrapper.eq(ApiInfo::getMethod, method);
        }
        
        if (StringUtils.hasText(path)) {
            queryWrapper.like(ApiInfo::getPath, path);
        }
        
        if (groupId != null) {
            queryWrapper.eq(ApiInfo::getGroupId, groupId);
        }
        
        if (devStatus != null) {
            queryWrapper.eq(ApiInfo::getDevStatus, devStatus.name());
        }
        
        // 过滤未删除的数据 (由于使用了@TableLogic注解，这里不需要显式添加)
        // queryWrapper.eq(ApiInfo::getIsDeleted, 0);
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(ApiInfo::getCreateTime);
        
        return apiInfoMapper.selectPage(pageParam, queryWrapper);
    }

    @Override
    public ApiInfo getApiDetail(Integer id) {
        return apiInfoMapper.selectById(id);
    }

    @Override
    public List<ApiInfo> getApisByGroupId(Integer groupId) {
        LambdaQueryWrapper<ApiInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApiInfo::getGroupId, groupId);
        queryWrapper.orderByDesc(ApiInfo::getCreateTime);
        // 过滤未删除的数据 (由于使用了@TableLogic注解，这里不需要显式添加)
        // queryWrapper.eq(ApiInfo::getIsDeleted, 0);
        return apiInfoMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createApi(ApiInfo apiInfo) {
        apiInfo.setCreateTime(LocalDateTime.now());
        apiInfo.setUpdateTime(LocalDateTime.now());
        apiInfo.setCreateBy(StpUtil.getLoginIdAsInt());
        // 如果没有设置开发状态，默认为"开发中"
        if (apiInfo.getDevStatus() == null) {
            apiInfo.setDevStatus(ApiDevStatus.DEVELOPING.name());
        }
        
        apiInfo.setIsDeleted(0); // 设置为未删除状态
        
        int result = apiInfoMapper.insert(apiInfo);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateApi(ApiInfo apiInfo) {
        apiInfo.setUpdateTime(LocalDateTime.now());
        
        int result = apiInfoMapper.updateById(apiInfo);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteApi(Integer id) {
        // 由于使用了@TableLogic注解实现逻辑删除，不再需要手动删除关联的参数数据
        // 直接调用mybatis-plus的删除方法，会自动将isDeleted设置为1，实现逻辑删除
        int result = apiInfoMapper.deleteById(id);
        return result > 0;
    }

    @Override
    public boolean updateApiDevStatus(Integer id, ApiDevStatus devStatus) {
        ApiInfo apiInfo = new ApiInfo();
        apiInfo.setId(id);
        apiInfo.setDevStatus(devStatus.name());
        apiInfo.setUpdateTime(LocalDateTime.now());
        
        int result = apiInfoMapper.updateById(apiInfo);
        return result > 0;
    }
    
    @Override
    public List<ApiGroupTreeVO> getGroupTreeWithApis(String devStatus) {
        // 获取所有分组
        LambdaQueryWrapper<ApiGroup> groupWrapper = new LambdaQueryWrapper<>();
        // 过滤未删除的数据 (由于使用了@TableLogic注解，这里不需要显式添加)
        // groupWrapper.eq(ApiGroup::getIsDeleted, 0);
        List<ApiGroup> allGroups = apiGroupMapper.selectList(groupWrapper);
        
        // 获取API列表，如果有状态筛选条件，则按状态筛选
        LambdaQueryWrapper<ApiInfo> apiWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(devStatus)) {
            apiWrapper.eq(ApiInfo::getDevStatus, devStatus);
        }
        // 过滤未删除的数据 (由于使用了@TableLogic注解，这里不需要显式添加)
        // apiWrapper.eq(ApiInfo::getIsDeleted, 0);
        List<ApiInfo> allApis = apiInfoMapper.selectList(apiWrapper);
        
        // 按分组ID分组
        Map<Integer, List<ApiInfo>> apisByGroup = allApis.stream()
                .collect(Collectors.groupingBy(ApiInfo::getGroupId));
        
        // 转换为树形结构
        return buildGroupTree(allGroups, apisByGroup);
    }
    
    /**
     * 构建分组树
     * @param allGroups 所有分组
     * @param apisByGroup 按分组ID分组的API
     * @return 分组树
     */
    private List<ApiGroupTreeVO> buildGroupTree(List<ApiGroup> allGroups, Map<Integer, List<ApiInfo>> apisByGroup) {
        // 创建分组ID到节点的映射
        Map<Integer, ApiGroupTreeVO> nodeMap = allGroups.stream()
                .map(group -> {
                    ApiGroupTreeVO node = new ApiGroupTreeVO();
                    BeanUtils.copyProperties(group, node);
                    node.setChildren(new ArrayList<>());
                    // 设置该分组下的API
                    node.setApis(apisByGroup.getOrDefault(group.getId(), new ArrayList<>()));
                    return node;
                })
                .collect(Collectors.toMap(ApiGroupTreeVO::getId, Function.identity()));
        
        // 构建树
        List<ApiGroupTreeVO> rootNodes = new ArrayList<>();
        
        for (ApiGroupTreeVO node : nodeMap.values()) {
            if (node.getParentId() == null || node.getParentId() == 0) {
                // 根节点
                rootNodes.add(node);
            } else {
                // 子节点，添加到父节点的children列表
                ApiGroupTreeVO parent = nodeMap.get(node.getParentId());
                if (parent != null) {
                    parent.getChildren().add(node);
                } else {
                    // 如果找不到父节点，作为根节点处理
                    rootNodes.add(node);
                }
            }
        }
        
        // 按sort字段排序
        sortTree(rootNodes);
        
        return rootNodes;
    }
    
    /**
     * 递归对树节点进行排序
     * @param nodes 节点列表
     */
    private void sortTree(List<ApiGroupTreeVO> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }
        
        // 按sort字段排序
        nodes.sort((a, b) -> {
            Integer sortA = a.getSort() != null ? a.getSort() : 0;
            Integer sortB = b.getSort() != null ? b.getSort() : 0;
            return sortA.compareTo(sortB);
        });
        
        // 递归排序子节点
        for (ApiGroupTreeVO node : nodes) {
            sortTree(node.getChildren());
        }
    }
} 