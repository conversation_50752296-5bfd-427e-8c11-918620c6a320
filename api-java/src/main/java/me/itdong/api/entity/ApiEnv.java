package me.itdong.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * API环境实体类
 */
@Data
@TableName("api_env")
public class ApiEnv {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 环境URL
     */
    private String envUrl;
    
    /**
     * 环境类型
     */
    private String envType;
    
    /**
     * 环境名称
     */
    private String envName;
} 