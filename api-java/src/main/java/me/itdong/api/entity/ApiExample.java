package me.itdong.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * API示例实体类
 */
@Data
@TableName("api_example")
public class ApiExample implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 示例ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 关联的API ID
     */
    private Integer apiId;
    
    /**
     * 示例类型(request/response)
     */
    private String type;
    
    /**
     * 示例名称
     */
    private String name;
    
    /**
     * 示例内容(JSON格式)
     */
    private String content;
    
    /**
     * 响应示例内容(JSON格式)
     */
    private String responseContent;
    
    /**
     * 是否默认示例(1:是, 0:否)
     */
    private Integer isDefault;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人ID
     */
    private Integer createBy;
    
    /**
     * 更新人ID
     */
    private Integer updateBy;
    
    /**
     * 是否删除(0:否, 1:是)
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;
} 