package me.itdong.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import me.itdong.api.enums.ApiParamType;

import java.io.Serializable;
import java.util.List;

/**
 * API参数实体类
 */
@Data
@TableName("api_parameter")
public class ApiParameter implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 参数ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 关联的API ID
     */
    private Integer apiId;

    /**
     * 参数名称
     */
    private String name;

    /**
     * 参数类型(string, number, boolean, object, array等)
     */
    private String type;

    /**
     * 参数位置(header, query, path, body, response)
     */
    private String paramType;

    /**
     * 参数描述
     */
    private String description;

    /**
     * 是否必需(1:是, 0:否)
     */
    private Integer required;

    /**
     * 示例值
     */
    private String example;

    /**
     * 父参数ID(用于嵌套参数)
     */
    private Integer parentId;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 是否删除(0:否, 1:是)
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    /**
     * 子参数列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<ApiParameter> children;

    /**
     * 参数类型枚举（非数据库字段）
     */
    @TableField(exist = false)
    private ApiParamType paramTypeEnum;

    /**
     * 获取参数类型枚举
     */
    public ApiParamType getParamTypeEnum() {
        return ApiParamType.fromString(this.paramType);
    }

    /**
     * 设置参数类型枚举
     */
    public void setParamTypeEnum(ApiParamType paramTypeEnum) {
        if (paramTypeEnum != null) {
            this.paramType = paramTypeEnum.name();
        }
    }
} 