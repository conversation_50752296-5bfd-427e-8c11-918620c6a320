package me.itdong.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import me.itdong.api.enums.ApiDevStatus;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * API更新历史记录
 */
@Data
@Accessors(chain = true)
@TableName("api_history")
public class ApiHistory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * API ID
     */
    private Integer apiId;

    /**
     * 旧状态
     */
    private String oldStatus;

    /**
     * 新状态
     */
    private String newStatus;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 备注说明
     */
    private String remarks;

    /**
     * 是否删除(0:否, 1:是)
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;
} 