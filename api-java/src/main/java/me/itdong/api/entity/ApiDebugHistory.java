package me.itdong.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * API调试历史实体类
 */
@Data
@TableName("api_debug_history")
public class ApiDebugHistory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 历史ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 关联的API ID
     */
    private Integer apiId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 请求头
     */
    private String requestHeaders;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 请求体
     */
    private String requestBody;

    /**
     * 响应状态码
     */
    private Integer responseStatus;

    /**
     * 响应头
     */
    private String responseHeaders;

    /**
     * 响应体
     */
    private String responseBody;

    /**
     * 请求耗时(ms)
     */
    private Integer duration;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 