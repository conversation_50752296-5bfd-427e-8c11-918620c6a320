package me.itdong.api.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 * 用户表(Users)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-13 19:28:18
 */
@Data
public class Users {
    //用户ID
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    private Integer userId;
    private String avatar;
    //用户名
    @ExcelProperty("姓名")
    private String username;
    //密码
    @ExcelProperty("密码")
    private String password;
    //真实姓名
    @ExcelProperty("真实姓名")
    private String name;
    //创建时间
    @ExcelIgnore
    private Date createdTime;
    //更新时间
    @ExcelIgnore
    private Date updatedTime;
}

