package me.itdong.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API分组实体类
 */
@Data
@TableName("api_group")
public class ApiGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分组ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 分组描述
     */
    private String description;

    /**
     * 父级分组ID
     */
    private Integer parentId;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    private Integer createBy;

    /**
     * 更新人ID
     */
    private Integer updateBy;

    /**
     * 是否删除(0:否, 1:是)
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    /**
     * 子分组列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<ApiGroup> children;

    /**
     * API列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<ApiInfo> apis;

    /**
     * 获取排序值，默认为0
     */
    public Integer getSort() {
        return sortOrder != null ? sortOrder : 0;
    }
} 