package me.itdong.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import me.itdong.api.enums.ApiDevStatus;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API信息实体类
 */
@Data
@TableName("api_info")
public class ApiInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * API ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * API名称
     */
    private String name;

    /**
     * 请求方法
     */
    private String method;

    /**
     * API路径
     */
    private String path;

    /**
     * API描述
     */
    private String description;

    /**
     * 所属分组ID
     */
    private Integer groupId;

    /**
     * 状态(1:启用, 0:禁用)
     */
    private Integer status;

    /**
     * 开发状态
     */
    private String devStatus;

    /**
     * 内容类型
     */
    private String contentType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    private Integer createBy;

    /**
     * 更新人ID
     */
    private Integer updateBy;

    /**
     * 是否删除(0:否, 1:是)
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    /**
     * 参数列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<ApiParameter> parameters;

    /**
     * 开发状态枚举（非数据库字段）
     */
    @TableField(exist = false)
    @JsonIgnore
    private ApiDevStatus devStatusEnum;

    /**
     * 获取开发状态枚举
     */
    public ApiDevStatus getDevStatusEnum() {
        return ApiDevStatus.fromString(this.devStatus);
    }

    /**
     * 设置开发状态枚举
     */
    public void setDevStatusEnum(ApiDevStatus devStatusEnum) {
        if (devStatusEnum != null) {
            this.devStatus = devStatusEnum.name();
        }
    }

    /**
     * 获取Long类型的API ID
     */
    public Long getLongId() {
        return id == null ? null : Long.valueOf(id);
    }
} 