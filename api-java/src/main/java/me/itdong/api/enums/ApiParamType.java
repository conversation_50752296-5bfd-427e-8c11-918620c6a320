package me.itdong.api.enums;

/**
 * API参数类型枚举
 */
public enum ApiParamType {
    /**
     * 请求头参数
     */
    HEADER("请求头参数"),

    /**
     * 查询参数
     */
    QUERY("查询参数"),

    /**
     * 路径参数
     */
    PATH("路径参数"),

    /**
     * 请求体参数
     */
    BODY("请求体参数"),

    /**
     * 响应参数
     */
    RESPONSE("响应参数");

    private final String description;

    ApiParamType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static ApiParamType fromString(String value) {
        if (value == null) {
            return null;
        }

        for (ApiParamType type : ApiParamType.values()) {
            if (type.name().equalsIgnoreCase(value)) {
                return type;
            }
        }
        return null;
    }
} 