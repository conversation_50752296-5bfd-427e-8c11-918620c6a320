package me.itdong.api.enums;

/**
 * 业务异常状态枚举
 */
public enum BusinessExceptionEnum {
    // 通用错误
    SUCCESS(200, "操作成功"),
    SYSTEM_ERROR(500, "系统错误"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "无权限操作"),
    
    // 用户相关错误
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    PASSWORD_ERROR(1003, "密码错误"),
    ACCOUNT_LOCKED(1004, "账号已锁定"),
    LOGIN_EXPIRED(1005, "登录已过期"),
    INVALID_TOKEN(1006, "无效的Token"),
    ROLE_NOT_FOUND(1007, "角色不存在"),
    
    // 文件相关错误
    FILE_UPLOAD_ERROR(2001, "文件上传失败"),
    FILE_TYPE_ERROR(2002, "文件类型错误"),
    FILE_SIZE_EXCEEDED(2003, "文件大小超出限制"),
    
    // 业务验证错误
    OPERATION_FAILED(3001, "操作失败"),
    DATA_NOT_FOUND(3002, "数据不存在"),
    DATA_ALREADY_EXISTS(3003, "数据已存在"),
    DATA_STATUS_ERROR(3004, "数据状态错误");

    private final int code;
    private final String message;

    BusinessExceptionEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 根据错误码获取枚举
     */
    public static BusinessExceptionEnum getByCode(int code) {
        for (BusinessExceptionEnum error : values()) {
            if (error.getCode() == code) {
                return error;
            }
        }
        return SYSTEM_ERROR;
    }
} 