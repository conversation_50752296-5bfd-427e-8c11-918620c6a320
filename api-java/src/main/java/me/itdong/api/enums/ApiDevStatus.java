package me.itdong.api.enums;

/**
 * API开发状态枚举
 */
public enum ApiDevStatus {
    /**
     * 开发中
     */
    DEVELOPING("开发中"),

    /**
     * 测试中
     */
    TESTING("测试中"),
    
    /**
     * 待对接
     */
    TO_CONNECT("待对接"),

    /**
     * 对接中
     */
    CONNECTING("对接中"),

    /**
     * 已完成
     */
    COMPLETED("完成");

    private final String description;

    ApiDevStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
    
    /**
     * 获取显示名称
     * @return 显示名称
     */
    public String getDisplayName() {
        return description;
    }

    public static ApiDevStatus fromString(String value) {
        if (value == null) {
            return null;
        }

        for (ApiDevStatus status : ApiDevStatus.values()) {
            if (status.name().equalsIgnoreCase(value)) {
                return status;
            }
        }
        return null;
    }
} 