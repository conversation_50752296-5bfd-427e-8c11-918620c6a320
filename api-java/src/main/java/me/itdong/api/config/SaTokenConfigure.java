package me.itdong.api.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.strategy.SaFirewallStrategy;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {
    String[] whitePaths = {
            "/.well-known/openid-configuration"
    };

    // 注册拦截器
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //重写白名单配置
        SaFirewallStrategy.instance.whitePaths = whitePaths;
//         注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验。
        registry.addInterceptor(new SaInterceptor(handle -> StpUtil.checkLogin()))
                .addPathPatterns("/**")
                .excludePathPatterns("/user/doLogin")
                .excludePathPatterns("/oauth2/**");
//        registry.addInterceptor(new SaInterceptor(handle -> StpUtil.checkLogin()))
//                .excludePathPatterns("/**");
    }

//    @Override
//    public void addCorsMappings(CorsRegistry registry) {
//        registry.addMapping("/**") // 对所有接口进行跨域配置
//                .allowedOriginPatterns("*") // 允许所有域进行跨域访问
//                .allowedMethods("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS") // 允许的请求方法
//                .allowedHeaders("*") // 允许的请求头
//                .exposedHeaders("*") // 允许暴露所有响应头
//                .allowCredentials(true) // 是否允许携带 Cookie 等认证信息
//                .maxAge(3600); // 预检请求的缓存时间（秒）
//    }
}

