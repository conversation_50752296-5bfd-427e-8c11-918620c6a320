package me.itdong.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.itdong.api.entity.Permissions;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限表(Permissions)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-14 18:06:34
 */
public interface PermissionsMapper extends BaseMapper<Permissions> {

    /**
     * 获取用户的所有权限
     * @param userId 用户ID
     * @return 权限名称列表
     */
    List<String> getall(String userId);
    
    /**
     * 获取角色的所有权限
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<Permissions> getRolePermissions(Integer roleId);
    
    /**
     * 为角色分配权限
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 影响的行数
     */
    int assignPermissionToRole(@Param("roleId") Integer roleId, @Param("permissionId") Integer permissionId);
    
    /**
     * 移除角色的权限
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 影响的行数
     */
    int removePermissionFromRole(@Param("roleId") Integer roleId, @Param("permissionId") Integer permissionId);
    
    /**
     * 删除角色的所有权限
     * @param roleId 角色ID
     * @return 影响的行数
     */
    int removeAllPermissionsFromRole(Integer roleId);
}

