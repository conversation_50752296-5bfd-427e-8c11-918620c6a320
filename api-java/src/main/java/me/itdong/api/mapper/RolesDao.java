package me.itdong.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.itdong.api.entity.Roles;
import me.itdong.api.entity.Users;
import org.apache.ibatis.annotations.MapKey;

import java.util.List;
import java.util.Map;

/**
 * 角色表(Roles)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-14 18:13:31
 */
public interface RolesDao extends BaseMapper<Roles> {

    List<String> getall(String userId);

    /**
     * 批量插入角色
     * @param entities 角色列表
     * @return 影响的行数
     */
    int insertBatch(List<Roles> entities);

    /**
     * 批量插入或更新角色
     * @param entities 角色列表
     * @return 影响的行数
     */
    int insertOrUpdateBatch(List<Roles> entities);

    /**
     * 删除用户的所有角色
     * @param userId 用户ID
     * @return 影响的行数
     */
    int deleteUserRoles(String userId);

    /**
     * 添加用户角色
     * @param userId 用户ID
     * @param role 角色名称
     * @return 影响的行数
     */
    int addUserRole(String userId, String role);

    /**
     * 获取角色下的用户列表
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<Users> getRoleUsers(Integer roleId);
}

