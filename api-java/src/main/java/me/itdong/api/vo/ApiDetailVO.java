package me.itdong.api.vo;

import lombok.Data;
import me.itdong.api.entity.ApiExample;
import me.itdong.api.entity.ApiInfo;
import me.itdong.api.entity.ApiParameter;

import java.util.List;

/**
 * API详细信息VO
 */
@Data
public class ApiDetailVO {
    
    /**
     * API基本信息
     */
    private ApiInfo apiInfo;
    
    /**
     * API参数列表
     */
    private List<ApiParameter> parameters;
    
    /**
     * 请求示例列表
     */
    private List<ApiExample> requestExamples;
    
    /**
     * 响应示例列表
     */
    private List<ApiExample> responseExamples;
    
    /**
     * 默认请求示例
     */
    private ApiExample defaultRequestExample;
    
    /**
     * 默认响应示例
     */
    private ApiExample defaultResponseExample;
} 