package me.itdong.api.vo;

import me.itdong.api.entity.ApiInfo;

import java.util.List;

/**
 * API分组树视图对象
 */
public class ApiGroupTreeVO {
    
    /**
     * 分组ID
     */
    private Integer id;
    
    /**
     * 分组名称
     */
    private String name;
    
    /**
     * 分组描述
     */
    private String description;
    
    /**
     * 父分组ID
     */
    private Integer parentId;
    
    /**
     * 排序顺序
     */
    private Integer sort;
    
    /**
     * 子分组列表
     */
    private List<ApiGroupTreeVO> children;
    
    /**
     * 分组下的API列表
     */
    private List<ApiInfo> apis;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public List<ApiGroupTreeVO> getChildren() {
        return children;
    }

    public void setChildren(List<ApiGroupTreeVO> children) {
        this.children = children;
    }

    public List<ApiInfo> getApis() {
        return apis;
    }

    public void setApis(List<ApiInfo> apis) {
        this.apis = apis;
    }
} 