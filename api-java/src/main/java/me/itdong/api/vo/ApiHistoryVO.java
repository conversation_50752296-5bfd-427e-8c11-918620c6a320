package me.itdong.api.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * API更新历史记录VO
 */
@Data
public class ApiHistoryVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * API ID
     */
    private Integer apiId;

    /**
     * API名称
     */
    private String apiName;

    /**
     * 旧状态
     */
    private String oldStatus;

    /**
     * 旧状态名称
     */
    private String oldStatusName;

    /**
     * 新状态
     */
    private String newStatus;

    /**
     * 新状态名称
     */
    private String newStatusName;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 备注说明
     */
    private String remarks;
} 