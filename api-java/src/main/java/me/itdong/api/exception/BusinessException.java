package me.itdong.api.exception;

import me.itdong.api.enums.BusinessExceptionEnum;

/**
 * 业务异常类
 */
public class BusinessException extends RuntimeException {

    private final int code;
    private final String message;

    public BusinessException(BusinessExceptionEnum exceptionEnum) {
        super(exceptionEnum.getMessage());
        this.code = exceptionEnum.getCode();
        this.message = exceptionEnum.getMessage();
    }

    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
} 