package me.itdong.api.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.dev33.satoken.util.SaResult;
import lombok.extern.slf4j.Slf4j;
import me.itdong.api.enums.BusinessExceptionEnum;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public SaResult handleBusinessException(BusinessException e) {
        log.error("业务异常: {}", e.getMessage());
        return new SaResult(e.getCode(), e.getMessage(), null);
    }

    /**
     * 处理未登录异常
     */
    @ExceptionHandler(NotLoginException.class)
    public SaResult handleNotLoginException(NotLoginException e) {
        log.error("未登录异常: {}", e.getMessage());
        return new SaResult(BusinessExceptionEnum.UNAUTHORIZED.getCode(), "您还未登录，请先登录", null);
    }

    /**
     * 处理无权限异常
     */
    @ExceptionHandler(NotPermissionException.class)
    public SaResult handleNotPermissionException(NotPermissionException e) {
        log.error("权限不足: {}", e.getMessage());
        return new SaResult(BusinessExceptionEnum.FORBIDDEN.getCode(), "权限不足，无法访问", null);
    }

    /**
     * 处理无角色异常
     */
    @ExceptionHandler(NotRoleException.class)
    public SaResult handleNotRoleException(NotRoleException e) {
        log.error("角色不足: {}", e.getMessage());
        return new SaResult(BusinessExceptionEnum.FORBIDDEN.getCode(), "角色不足，无法访问", null);
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    public SaResult handleException(Exception e) {
        log.error("系统异常", e);
        return new SaResult(BusinessExceptionEnum.SYSTEM_ERROR.getCode(), "系统异常，请联系管理员", null);
    }
} 