package me.itdong.api.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import me.itdong.api.annotation.OperationLog;
import me.itdong.api.entity.Users;
import me.itdong.api.entity.vo.UserCreateVO;
import me.itdong.api.entity.vo.UserListVo;
import me.itdong.api.entity.vo.UserLoginVo;
import me.itdong.api.enums.FilePurposeEnum;
import me.itdong.api.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * (User)表控制层
 *
 * <AUTHOR>
 * @since 2024-07-12 21:36:42
 */
@RestController
@RequestMapping("user")
public class UserController {
    /**
     * 服务对象
     */
    @Autowired
    private UserService userService;


    @PostMapping("/doLogin")
    @SaCheckPermission("user:login")
    @OperationLog(operationModule = "用户管理", operationType = "登录", operationDesc = "账号密码登录")
    public SaResult doLogin(@RequestBody UserLoginVo user) {
        Integer id = userService.login(user);
        if (id != null) {
            StpUtil.login(id);
            return SaResult.data(StpUtil.getTokenInfo());
        }
        return SaResult.error("登录失败");
    }

    /**
     * 创建新用户
     * @param userCreate 用户创建信息（包含用户名、密码、真实姓名和角色ID列表）
     * @return 创建结果
     */
    @PostMapping("/create")
    @SaCheckPermission("user:create")
    @OperationLog(operationModule = "用户管理", operationType = "创建", operationDesc = "创建新用户")
    public SaResult createUser(@RequestBody UserCreateVO userCreate) {
        // 参数校验
        if (userCreate.getUsername() == null || userCreate.getUsername().trim().isEmpty()) {
            return SaResult.error("用户名不能为空");
        }
        if (userCreate.getPassword() == null || userCreate.getPassword().trim().isEmpty()) {
            return SaResult.error("密码不能为空");
        }
        if (userCreate.getName() == null || userCreate.getName().trim().isEmpty()) {
            return SaResult.error("真实姓名不能为空");
        }
        if (userCreate.getRoleIds() == null || userCreate.getRoleIds().isEmpty()) {
            return SaResult.error("用户角色不能为空");
        }
        
        try {
            boolean success = userService.createUser(userCreate);
            if (success) {
                return SaResult.ok("用户创建成功");
            } else {
                return SaResult.error("用户创建失败");
            }
        } catch (Exception e) {
            return SaResult.error("用户创建失败：" + e.getMessage());
        }
    }

    @GetMapping("/checkLogin")
    @SaCheckPermission("user:check")
    @OperationLog(operationModule = "用户管理", operationType = "登录状态检查", operationDesc = "登录状态检查")
    public SaResult checkLogin() {
        StpUtil.checkLogin();
        return SaResult.data(StpUtil.getTokenInfo());
    }

    @GetMapping("/doLogout")
    @SaCheckPermission("user:logout")
    @OperationLog(operationModule = "用户管理", operationType = "注销", operationDesc = "注销")
    public SaResult doLogout() {
        StpUtil.logout(StpUtil.getLoginId());
        return SaResult.ok("退出成功");
    }

    /**
     * @return userinfo
     */
    @GetMapping("/getUserInfo")
    @SaCheckPermission("user:info")
    @OperationLog(operationModule = "用户管理", operationType = "用户信息获取", operationDesc = "用户信息获取")
    public SaResult getUserInfo() {
        return SaResult.data(userService.getUserInfo(StpUtil.getLoginIdAsInt()));
    }

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 更新结果
     */
    @PostMapping("/update")
    @SaCheckPermission("user:update")
    @OperationLog(operationModule = "用户管理", operationType = "用户数据更新", operationDesc = "用户数据更新")
    public SaResult update(@RequestBody UserListVo user) {
        if (user.getUserId() == null) {
            return SaResult.error("用户ID不能为空");
        }

        // 验证头像
        if (user.getAvatar() != null) {
            String avatarExt = user.getAvatar().substring(user.getAvatar().lastIndexOf(".") + 1).toLowerCase();
            if (!FilePurposeEnum.AVATAR.getAllowedExtensions().contains(avatarExt)) {
                return SaResult.error("头像格式不正确，仅支持: " + String.join(", ", FilePurposeEnum.AVATAR.getAllowedExtensions()));
            }
        }


        // 只有超级管理员可以更新用户角色
        if (StpUtil.hasRole("superAdmin")) {
            try {
                boolean success = userService.updateUserInfo(user);
                if (success) {
                    return SaResult.ok("更新成功");
                } else {
                    return SaResult.error("更新失败");
                }
            } catch (Exception e) {
                return SaResult.error(e.getMessage());
            }
        } else {
            // 如果不是超级管理员，只能修改自己的基本信息，不能修改角色
            if (!user.getUserId().equals(StpUtil.getLoginIdAsInt())) {
                return SaResult.error("权限不足，您只能修改自己的信息");
            }
            // 清除角色信息，防止非管理员修改角色
            user.setRoles(null);
            try {
                boolean success = userService.updateUserInfo(user);
                if (success) {
                    return SaResult.ok("更新成功");
                } else {
                    return SaResult.error("更新失败");
                }
            } catch (Exception e) {
                return SaResult.error(e.getMessage());
            }
        }
    }
    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户名称
     */
    @GetMapping("/getUserName/{userId}")
    @OperationLog(operationModule = "用户管理", operationType = "查询", operationDesc = "通过ID查询用户名")
    public SaResult getUserName(@PathVariable Integer userId) {
        String name = userService.getUserNameById(userId);
        return SaResult.data(name);
    }

    /**
     * 获取用户列表
     * @return 用户列表
     */
    @GetMapping("/list")
    @SaCheckPermission("user:list")
    @OperationLog(operationModule = "用户管理", operationType = "查询", operationDesc = "获取用户列表")
    public SaResult listUsers() {
        List<UserListVo> users = userService.getUserList();
        return SaResult.data(users);
    }
}


