package me.itdong.api.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import me.itdong.api.annotation.OperationLog;
import me.itdong.api.entity.Roles;
import me.itdong.api.service.RolesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * (Roles)表控制层
 *
 * <AUTHOR>
 * @since 2024-07-14 18:12:31
 */
@RestController
@RequestMapping("role")
public class RoleController {
    /**
     * 服务对象
     */
    @Autowired
    private RolesService rolesService;

    /**
     * 获取角色列表
     * @return 角色列表
     */
    @GetMapping("/list")
    @SaCheckPermission("role:list")
    @OperationLog(operationModule = "角色管理", operationType = "查询", operationDesc = "获取角色列表")
    public SaResult listRoles() {
        List<Roles> roles = rolesService.getRoleList();
        return SaResult.data(roles);
    }
} 