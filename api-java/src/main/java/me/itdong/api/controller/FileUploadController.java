package me.itdong.api.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import me.itdong.api.enums.FilePurposeEnum;
import me.itdong.api.service.FileUploadService;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
public class FileUploadController {

    @Autowired
    private FileUploadService fileUploadService;

    /**
     * 文件上传接口
     *
     * @param file    文件
     * @param purpose 文件用途，如：avatar（头像）、card（一卡通）等
     * @return 文件访问路径
     */
    @PostMapping("/upload")
    @SaCheckPermission("file:upload")
    public SaResult uploadFile(@RequestParam("file") MultipartFile file,
                               @RequestParam(value = "purpose", defaultValue = "other")
                               String purpose) {
        try {
            // 获取并验证文件用途
            FilePurposeEnum purposeEnum = FilePurposeEnum.getByCode(purpose);
            if (purposeEnum == FilePurposeEnum.OTHER && !purpose.equals("other")) {
                return SaResult.error("非法的文件用途");
            }

            // 获取文件后缀名并验证
            String extension = FilenameUtils.getExtension(file.getOriginalFilename()).toLowerCase();
            if (!purposeEnum.isExtensionAllowed(extension)) {
                return SaResult.error("不支持的文件类型，当前用途[" + purposeEnum.getDescription() + "]允许的文件类型为：" + String.join(", ", purposeEnum.getAllowedExtensions()));
            }

            String filePath = fileUploadService.saveFile(file, purposeEnum);
            return SaResult.data(filePath);
        } catch (Exception e) {
            return SaResult.error("文件上传失败：" + e.getMessage());
        }
    }
}