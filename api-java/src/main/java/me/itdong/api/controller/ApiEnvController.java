package me.itdong.api.controller;

import me.itdong.api.entity.ApiEnv;
import me.itdong.api.service.ApiEnvService;
import cn.dev33.satoken.annotation.SaCheckLogin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.util.SaResult;

import java.util.List;

/**
 * API环境控制器
 */
@RestController
@RequestMapping("/api/env")
public class ApiEnvController {

    @Autowired
    private ApiEnvService apiEnvService;

    /**
     * 获取所有环境
     */
    @SaCheckLogin
    @GetMapping("/list")
    public SaResult list() {
        List<ApiEnv> list = apiEnvService.list();
        return SaResult.data(list);
    }

    /**
     * 添加环境
     */
    @SaCheckLogin
    @PostMapping("/add")
    public SaResult add(@RequestBody ApiEnv apiEnv) {
        boolean success = apiEnvService.save(apiEnv);
        return success ? SaResult.ok("添加成功") : SaResult.error("添加失败");
    }

    /**
     * 更新环境
     */
    @SaCheckLogin
    @PostMapping("/update")
    public SaResult update(@RequestBody ApiEnv apiEnv) {
        boolean success = apiEnvService.updateById(apiEnv);
        return success ? SaResult.ok("更新成功") : SaResult.error("更新失败");
    }

    /**
     * 删除环境
     */
    @SaCheckLogin
    @PostMapping("/delete/{id}")
    public SaResult delete(@PathVariable Integer id) {
        boolean success = apiEnvService.removeById(id);
        return success ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    }

    /**
     * 获取环境详情
     */
    @SaCheckLogin
    @GetMapping("/info/{id}")
    public SaResult info(@PathVariable Integer id) {
        ApiEnv apiEnv = apiEnvService.getById(id);
        return SaResult.data(apiEnv);
    }
} 