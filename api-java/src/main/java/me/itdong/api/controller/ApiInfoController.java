package me.itdong.api.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import me.itdong.api.annotation.OperationLog;
import me.itdong.api.entity.ApiExample;
import me.itdong.api.entity.ApiInfo;
import me.itdong.api.enums.ApiDevStatus;
import me.itdong.api.service.ApiExampleService;
import me.itdong.api.service.ApiHistoryService;
import me.itdong.api.service.ApiInfoService;
import me.itdong.api.service.ApiParameterService;
import me.itdong.api.vo.ApiDetailVO;
import me.itdong.api.vo.ApiGroupTreeVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * API信息控制器
 */
@RestController
@RequestMapping("/api/info")
public class ApiInfoController {

    private static final Logger log = LoggerFactory.getLogger(ApiInfoController.class);

    @Autowired
    private ApiInfoService apiInfoService;
    
    @Autowired
    private ApiParameterService apiParameterService;
    
    @Autowired
    private ApiExampleService apiExampleService;

    @Autowired
    private ApiHistoryService apiHistoryService;

    /**
     * 分页查询API信息
     * @param page 页码
     * @param size 每页大小
     * @param name 接口名称（可选）
     * @param method 请求方法（可选）
     * @param path 接口路径（可选）
     * @param groupId 分组ID（可选）
     * @param devStatus 开发状态（可选）
     * @return 分页结果
     */
    @GetMapping("/page")
    @SaCheckPermission("api:info:list")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "分页查询API信息")
    public SaResult pageApiInfo(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String method,
            @RequestParam(required = false) String path,
            @RequestParam(required = false) Integer groupId,
            @RequestParam(required = false) String devStatus) {
        
        ApiDevStatus devStatusEnum = ApiDevStatus.fromString(devStatus);
        
        IPage<ApiInfo> pageResult = apiInfoService.pageApiInfo(page, size, name, method, path, groupId, devStatusEnum);
        return SaResult.data(pageResult);
    }

    /**
     * 根据分组ID获取API列表
     * @param groupId 分组ID
     * @return API列表
     */
    @GetMapping("/group/{groupId}")
    @SaCheckPermission("api:info:list")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "获取分组下的API列表")
    public SaResult getApisByGroupId(@PathVariable Integer groupId) {
        List<ApiInfo> apis = apiInfoService.getApisByGroupId(groupId);
        return SaResult.data(apis);
    }

    /**
     * 获取API详情
     * @param id API ID
     * @return API详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission("api:info:info")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "获取API详情")
    public SaResult getApiDetail(@PathVariable Integer id) {
        ApiInfo apiInfo = apiInfoService.getApiDetail(id);
        if (apiInfo == null) {
            return SaResult.error("API不存在");
        }
        return SaResult.data(apiInfo);
    }
    
    /**
     * 获取API完整信息（包含参数和示例）
     * @param id API ID
     * @return API完整信息
     */
    @GetMapping("/{id}/full")
    @SaCheckPermission("api:info:info")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "获取API完整信息")
    public SaResult getApiFullDetail(@PathVariable Integer id) {
        // 获取API基本信息
        ApiInfo apiInfo = apiInfoService.getApiDetail(id);
        if (apiInfo == null) {
            return SaResult.error("API不存在");
        }
        
        // 获取API参数树
        var parameters = apiParameterService.getParameterTreeByApiId(id);
        
        // 获取API示例
        List<ApiExample> requestExamples = apiExampleService.getExamplesByApiId(id, "request");
        List<ApiExample> responseExamples = apiExampleService.getExamplesByApiId(id, "response");
        
        // 获取默认示例
        ApiExample defaultRequestExample = apiExampleService.getDefaultExample(id, "request");
        ApiExample defaultResponseExample = apiExampleService.getDefaultExample(id, "response");
        
        // 组装完整信息
        ApiDetailVO detailVO = new ApiDetailVO();
        detailVO.setApiInfo(apiInfo);
        detailVO.setParameters(parameters);
        detailVO.setRequestExamples(requestExamples);
        detailVO.setResponseExamples(responseExamples);
        detailVO.setDefaultRequestExample(defaultRequestExample);
        detailVO.setDefaultResponseExample(defaultResponseExample);
        
        return SaResult.data(detailVO);
    }

    /**
     * 创建API
     * @param apiInfo API信息
     * @return 创建结果
     */
    @PostMapping
    @SaCheckPermission("api:info:create")
    @OperationLog(operationModule = "API管理", operationType = "创建", operationDesc = "创建API")
    public SaResult createApi(@RequestBody ApiInfo apiInfo) {
        // 参数校验
        if (apiInfo.getName() == null || apiInfo.getName().trim().isEmpty()) {
            return SaResult.error("API名称不能为空");
        }
        if (apiInfo.getMethod() == null || apiInfo.getMethod().trim().isEmpty()) {
            return SaResult.error("请求方法不能为空");
        }
        if (apiInfo.getPath() == null || apiInfo.getPath().trim().isEmpty()) {
            return SaResult.error("API路径不能为空");
        }
        if (apiInfo.getGroupId() == null) {
            return SaResult.error("所属分组不能为空");
        }
        
        boolean success = apiInfoService.createApi(apiInfo);
        return success ? SaResult.ok("创建成功").setData(apiInfo.getId()) : SaResult.error("创建失败");
    }

    /**
     * 更新API
     * @param id API ID
     * @param apiInfo API信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @SaCheckPermission("api:info:update")
    @OperationLog(operationModule = "API管理", operationType = "更新", operationDesc = "更新API")
    public SaResult updateApi(@PathVariable Integer id, @RequestBody ApiInfo apiInfo) {
        // 参数校验
        if (apiInfo.getName() == null || apiInfo.getName().trim().isEmpty()) {
            return SaResult.error("API名称不能为空");
        }
        if (apiInfo.getMethod() == null || apiInfo.getMethod().trim().isEmpty()) {
            return SaResult.error("请求方法不能为空");
        }
        if (apiInfo.getPath() == null || apiInfo.getPath().trim().isEmpty()) {
            return SaResult.error("API路径不能为空");
        }
        
        apiInfo.setId(id);
        boolean success = apiInfoService.updateApi(apiInfo);
        return success ? SaResult.ok("更新成功") : SaResult.error("更新失败");
    }

    /**
     * 删除API
     * @param id API ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission("api:info:delete")
    @OperationLog(operationModule = "API管理", operationType = "删除", operationDesc = "删除API")
    public SaResult deleteApi(@PathVariable Integer id) {
        boolean success = apiInfoService.deleteApi(id);
        return success ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    }

    /**
     * 更新API开发状态
     * @param id API ID
     * @param devStatus 开发状态
     * @return 更新结果
     */
    @PutMapping("/{id}/dev-status")
    @SaCheckPermission("api:info:update")
    @OperationLog(operationModule = "API管理", operationType = "更新", operationDesc = "更新API开发状态")
    public SaResult updateApiDevStatus(
            @PathVariable Integer id, 
            @RequestParam String devStatus,
            @RequestParam(required = false) String remarks) {
        
        ApiDevStatus devStatusEnum = ApiDevStatus.fromString(devStatus);
        if (devStatusEnum == null) {
            return SaResult.error("无效的开发状态");
        }
        
        // 获取当前API信息
        ApiInfo apiInfo = apiInfoService.getApiDetail(id);
        if (apiInfo == null) {
            return SaResult.error("API不存在");
        }
        
        // 获取原开发状态
        ApiDevStatus oldStatus = null;
        if (apiInfo.getDevStatus() != null) {
            oldStatus = ApiDevStatus.fromString(apiInfo.getDevStatus());
        }
        
        // 更新API开发状态
        boolean success = apiInfoService.updateApiDevStatus(id, devStatusEnum);
        
        // 如果更新成功，添加历史记录
        if (success && apiHistoryService != null) {
            // 获取当前登录用户ID
            Long operatorId = null;
            try {
                Object loginIdObj = StpUtil.getLoginId();
                if (loginIdObj instanceof Number) {
                    operatorId = ((Number) loginIdObj).longValue();
                } else if (loginIdObj != null) {
                    operatorId = Long.parseLong(loginIdObj.toString());
                }
            } catch (Exception e) {
                log.warn("获取当前登录用户ID失败", e);
            }
            
            apiHistoryService.addStatusChangeRecord(id, oldStatus, devStatusEnum, operatorId, remarks);
        }
        
        return success ? SaResult.ok("更新成功") : SaResult.error("更新失败");
    }

    /**
     * 获取API开发状态枚举信息
     * @return 开发状态枚举列表
     */
    @GetMapping("/dev-status/enum")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "获取API开发状态枚举")
    public SaResult getApiDevStatusEnum() {
        List<Map<String, Object>> statusList = Arrays.stream(ApiDevStatus.values())
                .map(status -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("code", status.name());
                    map.put("name", status.getDisplayName());
                    map.put("description", status.getDescription());
                    map.put("type", "status");
                    return map;
                })
                .collect(Collectors.toList());
        
        // 创建树状结构的根节点
        Map<String, Object> rootNode = new HashMap<>();
        rootNode.put("code", "ROOT");
        rootNode.put("name", "状态");
        rootNode.put("type", "group");
        rootNode.put("children", statusList);
        
        List<Map<String, Object>> resultList = new ArrayList<>();
        resultList.add(rootNode);
        
        return SaResult.data(resultList);
    }
    
    /**
     * 获取按开发状态筛选的API树
     * @param devStatus 开发状态（可选）
     * @return API树结构
     */
    @GetMapping("/tree")
    @SaCheckPermission("api:info:list")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "获取API树")
    public SaResult getApiTree(@RequestParam(required = false) String devStatus) {
        // 先获取分组树
        List<ApiGroupTreeVO> groupTree = apiInfoService.getGroupTreeWithApis(devStatus);
        return SaResult.data(groupTree);
    }
} 