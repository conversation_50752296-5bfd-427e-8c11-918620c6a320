package me.itdong.api.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import me.itdong.api.annotation.OperationLog;
import me.itdong.api.entity.ApiGroup;
import me.itdong.api.service.ApiGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * API分组控制器
 */
@RestController
@RequestMapping("/api/group")
public class ApiGroupController {

    @Autowired
    private ApiGroupService apiGroupService;

    /**
     * 获取API分组树
     * @return API分组树列表
     */
    @GetMapping("/tree")
    @SaCheckPermission("api:group:list")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "获取API分组树")
    public SaResult getGroupTree() {
        List<ApiGroup> tree = apiGroupService.getGroupTree();
        return SaResult.data(tree);
    }

    /**
     * 获取分组详情
     * @param id 分组ID
     * @return 分组详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission("api:group:info")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "获取API分组详情")
    public SaResult getGroupDetail(@PathVariable Integer id) {
        ApiGroup group = apiGroupService.getGroupDetail(id);
        if (group == null) {
            return SaResult.error("分组不存在");
        }
        return SaResult.data(group);
    }

    /**
     * 创建API分组
     * @param apiGroup API分组信息
     * @return 创建结果
     */
    @PostMapping
    @SaCheckPermission("api:group:create")
    @OperationLog(operationModule = "API管理", operationType = "创建", operationDesc = "创建API分组")
    public SaResult createGroup(@RequestBody ApiGroup apiGroup) {
        if (apiGroup.getName() == null || apiGroup.getName().trim().isEmpty()) {
            return SaResult.error("分组名称不能为空");
        }
        
        boolean success = apiGroupService.createGroup(apiGroup);
        return success ? SaResult.ok("创建成功") : SaResult.error("创建失败");
    }

    /**
     * 更新API分组
     * @param id 分组ID
     * @param apiGroup API分组信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @SaCheckPermission("api:group:update")
    @OperationLog(operationModule = "API管理", operationType = "更新", operationDesc = "更新API分组")
    public SaResult updateGroup(@PathVariable Integer id, @RequestBody ApiGroup apiGroup) {
        if (apiGroup.getName() == null || apiGroup.getName().trim().isEmpty()) {
            return SaResult.error("分组名称不能为空");
        }
        
        apiGroup.setId(id);
        boolean success = apiGroupService.updateGroup(apiGroup);
        return success ? SaResult.ok("更新成功") : SaResult.error("更新失败");
    }

    /**
     * 删除API分组
     * @param id 分组ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission("api:group:delete")
    @OperationLog(operationModule = "API管理", operationType = "删除", operationDesc = "删除API分组")
    public SaResult deleteGroup(@PathVariable Integer id) {
        try {
            boolean success = apiGroupService.deleteGroup(id);
            return success ? SaResult.ok("删除成功") : SaResult.error("删除失败");
        } catch (RuntimeException e) {
            return SaResult.error(e.getMessage());
        }
    }
} 