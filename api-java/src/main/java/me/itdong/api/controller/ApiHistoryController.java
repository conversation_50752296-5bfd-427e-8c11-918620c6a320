package me.itdong.api.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import me.itdong.api.annotation.OperationLog;
import me.itdong.api.entity.ApiHistory;
import me.itdong.api.service.ApiHistoryService;
import me.itdong.api.vo.ApiHistoryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * API更新历史记录控制器
 */
@RestController
@RequestMapping("/api/history")
public class ApiHistoryController {

    @Autowired
    private ApiHistoryService apiHistoryService;

    /**
     * 分页查询API历史记录
     * 
     * @param current 当前页
     * @param size 每页大小
     * @param apiId API ID
     * @return 分页结果
     */
    @GetMapping("/page")
    @SaCheckPermission("api:history:list")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "分页查询API更新历史")
    public SaResult pageApiHistory(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer apiId) {
        
        Page<ApiHistory> page = new Page<>(current, size);
        Page<ApiHistoryVO> result = apiHistoryService.pageApiHistory(page, apiId);
        
        return SaResult.data(result);
    }

    /**
     * 获取指定API的历史记录
     * 
     * @param apiId API ID
     * @return 历史记录列表
     */
    @GetMapping("/list/{apiId}")
    @SaCheckPermission("api:history:list")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "查询API更新历史")
    public SaResult getApiHistoryList(@PathVariable Integer apiId) {
        List<ApiHistoryVO> historyList = apiHistoryService.getApiHistoryList(apiId);
        return SaResult.data(historyList);
    }
} 