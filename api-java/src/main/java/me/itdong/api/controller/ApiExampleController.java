package me.itdong.api.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import me.itdong.api.annotation.OperationLog;
import me.itdong.api.entity.ApiExample;
import me.itdong.api.service.ApiExampleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * API示例控制器
 */
@RestController
@RequestMapping("/api/example")
public class ApiExampleController {

    @Autowired
    private ApiExampleService apiExampleService;

    /**
     * 根据API ID获取示例列表
     * @param apiId API ID
     * @param type 示例类型(request/response)，可选
     * @return 示例列表
     */
    @GetMapping("/list/{apiId}")
    @SaCheckPermission("api:example:list")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "获取API示例列表")
    public SaResult getExamplesByApiId(
            @PathVariable Integer apiId,
            @RequestParam(required = false) String type) {
        List<ApiExample> examples = apiExampleService.getExamplesByApiId(apiId, type);
        return SaResult.data(examples);
    }

    /**
     * 获取示例详情
     * @param id 示例ID
     * @return 示例详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission("api:example:info")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "获取API示例详情")
    public SaResult getExampleDetail(@PathVariable Integer id) {
        ApiExample example = apiExampleService.getById(id);
        if (example == null) {
            return SaResult.error("示例不存在");
        }
        return SaResult.data(example);
    }

    /**
     * 获取默认示例
     * @param apiId API ID
     * @param type 示例类型(request/response)
     * @return 默认示例
     */
    @GetMapping("/default/{apiId}")
    @SaCheckPermission("api:example:list")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "获取API默认示例")
    public SaResult getDefaultExample(
            @PathVariable Integer apiId,
            @RequestParam String type) {
        ApiExample example = apiExampleService.getDefaultExample(apiId, type);
        if (example == null) {
            return SaResult.error("默认示例不存在");
        }
        return SaResult.data(example);
    }

    /**
     * 创建API示例
     * @param example 示例信息
     * @return 创建结果
     */
    @PostMapping
    @SaCheckPermission("api:example:create")
    @OperationLog(operationModule = "API管理", operationType = "创建", operationDesc = "创建API示例")
    public SaResult createExample(@RequestBody ApiExample example) {
        // 参数校验
        if (example.getApiId() == null) {
            return SaResult.error("API ID不能为空");
        }
        if (example.getType() == null || example.getType().trim().isEmpty()) {
            return SaResult.error("示例类型不能为空");
        }
        if (example.getContent() == null || example.getContent().trim().isEmpty()) {
            return SaResult.error("请求示例内容不能为空");
        }
        // 响应内容可以为空，但如果提供了就不能为空字符串
        if (example.getResponseContent() != null && example.getResponseContent().trim().isEmpty()) {
            return SaResult.error("响应示例内容不能为空字符串");
        }
        
        boolean success = apiExampleService.saveExample(example);
        return success ? SaResult.ok("创建成功") : SaResult.error("创建失败");
    }

    /**
     * 更新API示例
     * @param id 示例ID
     * @param example 示例信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @SaCheckPermission("api:example:update")
    @OperationLog(operationModule = "API管理", operationType = "更新", operationDesc = "更新API示例")
    public SaResult updateExample(@PathVariable Integer id, @RequestBody ApiExample example) {
        // 参数校验
        if (example.getType() == null || example.getType().trim().isEmpty()) {
            return SaResult.error("示例类型不能为空");
        }
        if (example.getContent() == null || example.getContent().trim().isEmpty()) {
            return SaResult.error("请求示例内容不能为空");
        }
        // 响应内容可以为空，但如果提供了就不能为空字符串
        if (example.getResponseContent() != null && example.getResponseContent().trim().isEmpty()) {
            return SaResult.error("响应示例内容不能为空字符串");
        }
        
        example.setId(id);
        boolean success = apiExampleService.updateExample(example);
        return success ? SaResult.ok("更新成功") : SaResult.error("更新失败");
    }

    /**
     * 设置为默认示例
     * @param id 示例ID
     * @return 设置结果
     */
    @PutMapping("/{id}/default")
    @SaCheckPermission("api:example:update")
    @OperationLog(operationModule = "API管理", operationType = "更新", operationDesc = "设置默认API示例")
    public SaResult setDefaultExample(@PathVariable Integer id) {
        boolean success = apiExampleService.setDefaultExample(id);
        return success ? SaResult.ok("设置成功") : SaResult.error("设置失败");
    }

    /**
     * 删除API示例
     * @param id 示例ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission("api:example:delete")
    @OperationLog(operationModule = "API管理", operationType = "删除", operationDesc = "删除API示例")
    public SaResult deleteExample(@PathVariable Integer id) {
        boolean success = apiExampleService.removeById(id);
        return success ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    }

    /**
     * 删除API的所有示例
     * @param apiId API ID
     * @return 删除结果
     */
    @DeleteMapping("/all/{apiId}")
    @SaCheckPermission("api:example:delete")
    @OperationLog(operationModule = "API管理", operationType = "删除", operationDesc = "删除API的所有示例")
    public SaResult deleteAllExamples(@PathVariable Integer apiId) {
        boolean success = apiExampleService.deleteExamplesByApiId(apiId);
        return success ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    }
    
    /**
     * 批量保存API示例（新增、更新、删除）
     * @param examples 示例列表
     * @return 保存结果
     */
    @PostMapping("/batch")
    @SaCheckPermission("api:example:create")
    @OperationLog(operationModule = "API管理", operationType = "保存", operationDesc = "批量保存API示例")
    public SaResult batchSaveExamples(@RequestBody List<ApiExample> examples) {
        // 参数校验
        if (examples == null || examples.isEmpty()) {
            return SaResult.error("示例列表不能为空");
        }
        
        // 校验每个示例
        Integer apiId = null;
        for (ApiExample example : examples) {
            if (example.getApiId() == null) {
                return SaResult.error("API ID不能为空");
            }
            
            // 确保所有示例属于同一个API
            if (apiId == null) {
                apiId = example.getApiId();
            } else if (!apiId.equals(example.getApiId())) {
                return SaResult.error("所有示例必须属于同一个API");
            }
            
            if (example.getType() == null || example.getType().trim().isEmpty()) {
                return SaResult.error("示例类型不能为空");
            }
            if (example.getContent() == null || example.getContent().trim().isEmpty()) {
                return SaResult.error("请求示例内容不能为空");
            }
            // 响应内容可以为空，但如果提供了就不能为空字符串
            if (example.getResponseContent() != null && example.getResponseContent().trim().isEmpty()) {
                return SaResult.error("响应示例内容不能为空字符串");
            }
        }
        
        // 调用服务批量保存
        boolean success = apiExampleService.batchSaveExamples(apiId, examples);
        return success ? SaResult.ok("保存成功") : SaResult.error("保存失败");
    }
} 