package me.itdong.api.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import me.itdong.api.annotation.OperationLog;
import me.itdong.api.entity.ApiParameter;
import me.itdong.api.enums.ApiParamType;
import me.itdong.api.service.ApiParameterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * API参数控制器
 */
@RestController
@RequestMapping("/api/parameter")
public class ApiParameterController {

    @Autowired
    private ApiParameterService apiParameterService;

    /**
     * 根据API ID获取参数列表（树形结构）
     * @param apiId API ID
     * @return 参数列表
     */
    @GetMapping("/tree/{apiId}")
    @SaCheckPermission("api:parameter:list")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "获取API参数树")
    public SaResult getParameterTree(@PathVariable Integer apiId) {
        List<ApiParameter> tree = apiParameterService.getParameterTreeByApiId(apiId);
        return SaResult.data(tree);
    }

    /**
     * 根据API ID和参数类型获取参数列表
     * @param apiId API ID
     * @param paramType 参数类型
     * @return 参数列表
     */
    @GetMapping("/list/{apiId}")
    @SaCheckPermission("api:parameter:list")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "获取API参数列表")
    public SaResult getParametersByType(
            @PathVariable Integer apiId,
            @RequestParam(required = false) String paramType) {
        
        ApiParamType paramTypeEnum = ApiParamType.fromString(paramType);
        
        List<ApiParameter> parameters = apiParameterService.getParametersByApiIdAndType(apiId, paramTypeEnum);
        return SaResult.data(parameters);
    }

    /**
     * 获取参数详情
     * @param id 参数ID
     * @return 参数详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission("api:parameter:info")
    @OperationLog(operationModule = "API管理", operationType = "查询", operationDesc = "获取API参数详情")
    public SaResult getParameterDetail(@PathVariable Integer id) {
        ApiParameter parameter = apiParameterService.getById(id);
        if (parameter == null) {
            return SaResult.error("参数不存在");
        }
        return SaResult.data(parameter);
    }

    /**
     * 批量保存参数
     * @param apiId API ID
     * @param parameters 参数列表
     * @return 保存结果
     */
    @PostMapping("/batch/{apiId}")
    @SaCheckPermission("api:parameter:save")
    @OperationLog(operationModule = "API管理", operationType = "保存", operationDesc = "批量保存API参数")
    public SaResult saveParameters(@PathVariable Integer apiId, @RequestBody List<ApiParameter> parameters) {
        // 参数校验
        for (ApiParameter parameter : parameters) {
            if (parameter.getName() == null || parameter.getName().trim().isEmpty()) {
                return SaResult.error("参数名称不能为空");
            }
            if (parameter.getType() == null || parameter.getType().trim().isEmpty()) {
                return SaResult.error("参数类型不能为空");
            }
            if (parameter.getParamType() == null || parameter.getParamType().trim().isEmpty()) {
                return SaResult.error("参数位置不能为空");
            }
            
            // 设置API ID
            parameter.setApiId(apiId);
        }
        
        boolean success = apiParameterService.saveParameters(apiId, parameters);
        return success ? SaResult.ok("保存成功") : SaResult.error("保存失败");
    }

    /**
     * 创建单个参数
     * @param parameter 参数信息
     * @return 创建结果
     */
    @PostMapping("")
    @SaCheckPermission("api:parameter:save")
    @OperationLog(operationModule = "API管理", operationType = "创建", operationDesc = "创建API参数")
    public SaResult createParameter(@RequestBody ApiParameter parameter) {
        // 参数校验
        if (parameter.getName() == null || parameter.getName().trim().isEmpty()) {
            return SaResult.error("参数名称不能为空");
        }
        if (parameter.getType() == null || parameter.getType().trim().isEmpty()) {
            return SaResult.error("参数类型不能为空");
        }
        if (parameter.getParamType() == null || parameter.getParamType().trim().isEmpty()) {
            return SaResult.error("参数位置不能为空");
        }
        if (parameter.getApiId() == null) {
            return SaResult.error("API ID不能为空");
        }
        
        // 设置默认排序顺序
        if (parameter.getSortOrder() == null) {
            parameter.setSortOrder(0);
        }
        
        boolean success = apiParameterService.save(parameter);
        return success ? SaResult.ok("创建成功") : SaResult.error("创建失败");
    }

    /**
     * 更新单个参数
     * @param id 参数ID
     * @param parameter 参数信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @SaCheckPermission("api:parameter:update")
    @OperationLog(operationModule = "API管理", operationType = "更新", operationDesc = "更新API参数")
    public SaResult updateParameter(@PathVariable Integer id, @RequestBody ApiParameter parameter) {
        // 参数校验
        if (parameter.getName() == null || parameter.getName().trim().isEmpty()) {
            return SaResult.error("参数名称不能为空");
        }
        if (parameter.getType() == null || parameter.getType().trim().isEmpty()) {
            return SaResult.error("参数类型不能为空");
        }
        if (parameter.getParamType() == null || parameter.getParamType().trim().isEmpty()) {
            return SaResult.error("参数位置不能为空");
        }
        
        parameter.setId(id);
        boolean success = apiParameterService.updateById(parameter);
        return success ? SaResult.ok("更新成功") : SaResult.error("更新失败");
    }

    /**
     * 删除单个参数
     * @param id 参数ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission("api:parameter:delete")
    @OperationLog(operationModule = "API管理", operationType = "删除", operationDesc = "删除API参数")
    public SaResult deleteParameter(@PathVariable Integer id) {
        boolean success = apiParameterService.removeById(id);
        return success ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    }

    /**
     * 删除API的所有参数
     * @param apiId API ID
     * @return 删除结果
     */
    @DeleteMapping("/all/{apiId}")
    @SaCheckPermission("api:parameter:delete")
    @OperationLog(operationModule = "API管理", operationType = "删除", operationDesc = "删除API的所有参数")
    public SaResult deleteAllParameters(@PathVariable Integer apiId) {
        boolean success = apiParameterService.deleteParametersByApiId(apiId);
        return success ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    }
} 