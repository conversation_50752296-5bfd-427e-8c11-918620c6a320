<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="me.itdong.api.mapper.PermissionsMapper">
    <resultMap id="PermissionsMap" type="me.itdong.api.entity.Permissions">
        <result property="permissionId" column="permission_id" jdbcType="INTEGER"/>
        <result property="permissionName" column="permission_name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 获取用户的所有权限 -->
    <select id="getall" resultType="java.lang.String">
        SELECT
            p.permission_name
        FROM
            permissions p
                JOIN role_permissions rp ON p.permission_id = rp.permission_id
                JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE
            ur.user_id = #{userId}
    </select>
    
    <!-- 获取角色的所有权限 -->
    <select id="getRolePermissions" resultMap="PermissionsMap">
        SELECT
            p.*
        FROM
            permissions p
                JOIN role_permissions rp ON p.permission_id = rp.permission_id
        WHERE
            rp.role_id = #{roleId}
    </select>
    
    <!-- 为角色分配权限 -->
    <insert id="assignPermissionToRole">
        INSERT INTO role_permissions (role_id, permission_id)
        VALUES (#{roleId}, #{permissionId})
        ON DUPLICATE KEY UPDATE assigned_time = CURRENT_TIMESTAMP
    </insert>
    
    <!-- 移除角色的权限 -->
    <delete id="removePermissionFromRole">
        DELETE FROM role_permissions
        WHERE role_id = #{roleId} AND permission_id = #{permissionId}
    </delete>
    
    <!-- 删除角色的所有权限 -->
    <delete id="removeAllPermissionsFromRole">
        DELETE FROM role_permissions
        WHERE role_id = #{roleId}
    </delete>
</mapper>