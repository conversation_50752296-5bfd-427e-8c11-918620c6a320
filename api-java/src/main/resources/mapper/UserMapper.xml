<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="me.itdong.api.mapper.UserMapper">
<!--    <update id="updateFirstLogin">-->
<!--        UPDATE users SET isfirstlogin = true WHERE userId = #{userId};-->
<!--    </update>-->
    <select id="fuzzyQuery" resultType="me.itdong.api.entity.UserInfo">
        SELECT name, stu_id
        FROM users
        WHERE name LIKE CONCAT('%', #{fuzzy}, '%') OR stu_id LIKE CONCAT('%', #{fuzzy}, '%')
    </select>

    <select id="searchUsers" resultType="me.itdong.api.entity.vo.UserVo">
        SELECT user_id as userId, name, stu_id as stuId
        FROM users
        WHERE name LIKE CONCAT('%', #{query}, '%')
        OR stu_id LIKE CONCAT('%', #{query}, '%')
        LIMIT 10
    </select>

    <select id="selectBasicInfoByIds" resultType="java.util.Map">
        SELECT 
            user_id as userId,
            name,
            stu_id as stuId
        FROM users
        WHERE user_id IN
        <foreach collection="userIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectUserPage" resultType="me.itdong.api.entity.Users">
        SELECT *
        FROM users
        <where>
            <if test="query != null and query != ''">
                AND (name LIKE CONCAT('%', #{query}, '%')
                OR stu_id LIKE CONCAT('%', #{query}, '%')
                OR email LIKE CONCAT('%', #{query}, '%')
            </if>
            <if test="status != null">
                AND lab_status = #{status}
            </if>
        </where>
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="selectUserTotal" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM users
        <where>
            <if test="query != null and query != ''">
                AND (name LIKE CONCAT('%', #{query}, '%')
                OR stu_id LIKE CONCAT('%', #{query}, '%')
                OR email LIKE CONCAT('%', #{query}, '%')
            </if>
            <if test="status != null">
                AND lab_status = #{status}
            </if>
        </where>
    </select>

    <!-- 根据角色名称获取用户ID列表 -->
    <select id="getUserIdsByRole" resultType="java.lang.String">
        SELECT u.user_id
        FROM users u
        INNER JOIN user_roles ur ON u.user_id = ur.user_id
        INNER JOIN roles r ON ur.role_id = r.role_id
        WHERE r.role_id = #{roleId}
        AND u.lab_status = 1
    </select>
</mapper>